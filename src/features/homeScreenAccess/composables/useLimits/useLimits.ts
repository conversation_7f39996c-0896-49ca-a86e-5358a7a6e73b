import { computed, ref, watch } from 'vue';

import { useStore, useSelectedProductId, useGetter, useIsLoading } from '@/composibles';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

export default function useLimits() {
	const page = ref(1);
	const selectedItems = ref([]);
	const selectedUser = ref(null);
	const selectedProduct = ref();

	const filters = ref({
		userId: null,
		page: 0,
		size: 5,
	});
	const { dispatch } = useStore();

	const currentProductId = useSelectedProductId();
	const users = computed(useGetter('homeScreenAccess', 'usersListResult'));
	const usersOptions = computed(useGetter('homeScreenAccess', 'usersOptionsListResult'));
	const productsOptions = computed(useGetter('homeScreenAccess', 'productsOptionsListResult'));

	const usersRequestStatus = computed(useGetter('homeScreenAccess', 'usersRequestStatus'));
	const usersOptionsRequestStatus = computed(
		useGetter('homeScreenAccess', 'usersOptionsRequestStatus')
	);
	const productsOptionsRequestStatus = computed(
		useGetter('homeScreenAccess', 'productsOptionsRequestStatus')
	);
	const financialVehicleUserSettingsRequestStatus = computed(
		useGetter('homeScreenAccess', 'financialVehicleUserSettingsRequestStatus')
	);

	const isError = computed(() => {
		return usersRequestStatus.value.type === RequestStatusEnum.ERROR;
	});
	const isLoading = useIsLoading([
		usersRequestStatus,
		usersOptionsRequestStatus,
		productsOptionsRequestStatus,
	]);

	const fetchUsersOptions = id => {
		dispatch('homeScreenAccess/fetchUsersOptions', {
			idProduct: id,
		});
	};

	const fetchUsers = () => {
		dispatch('homeScreenAccess/fetchUsers', {
			idProduct: selectedProduct.value,
			filters: { ...filters.value },
		});
	};

	const updateFinancialVehicleUserSettings = payload => {
		dispatch('homeScreenAccess/updateFinancialVehicleUserSettings', {
			idProduct: selectedProduct.value,
			payload,
		});
	};

	const fetchProductsOptions = () => {
		dispatch('homeScreenAccess/fetchProductsOptions', {
			idProduct: currentProductId.value,
		});
	};

	const onChangePage = currentPage => {
		page.value = currentPage;
		filters.value = {
			...filters.value,
			page: currentPage - 1,
		};
		fetchUsers();
	};

	const onChangeLimitPerPage = limit => {
		filters.value = {
			...filters.value,
			size: limit,
			page: 0,
		};
		page.value = 1;
		fetchUsers();
	};

	const onSelectUser = data => {
		filters.value = {
			...filters.value,
			userId: data,
		};
		selectedItems.value = [];
		fetchUsers();
	};

	const onInputSelectUser = data => {
		filters.value = {
			...filters.value,
			userId: data,
		};
		fetchUsers();
	};

	const onSelectProduct = id => {
		selectedUser.value = null;
		selectedItems.value = [];
		filters.value = {
			...filters.value,
			userId: null,
		};
		fetchUsers();
		fetchUsersOptions(id);
	};

	watch(financialVehicleUserSettingsRequestStatus, newValue => {
		if (newValue === RequestStatusEnum.SUCCESS) {
			notification(RequestStatusEnum.SUCCESS, 'Configuração realizada com sucesso');
		}
	});



	return {
		page,
		users,
		usersOptions,
		isLoading,
		selectedUser,
		selectedProduct,
		productsOptions,
		selectedItems,
		isError,
		fetchUsers,
		fetchUsersOptions,
		fetchProductsOptions,
		onChangePage,
		onChangeLimitPerPage,
		onSelectUser,
		onInputSelectUser,
		onSelectProduct,
		updateFinancialVehicleUserSettings,
	};
}
