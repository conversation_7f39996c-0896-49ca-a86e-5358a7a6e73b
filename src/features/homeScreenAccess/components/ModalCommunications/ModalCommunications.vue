<template>
	<farm-modal
		size="md"
		v-model="value"
		v-if="value"
		:offsetTop="48"
		:offsetBottom="80"
		:persistent="true"
	>
		<template v-slot:header>
			<farm-dialog-header title="Configurar Comunicações" @onClose="$emit('onClose')" />
		</template>
		<template v-slot:content>
			<farm-caption v-if="showMessage" class="mb-4">
				Ao selecionar múltiplos usuários é necessário definir os programas novamente. As
				definições anteriores não serão consideradas.
			</farm-caption>
			<v-data-table
				hide-default-footer
				id="modal-communication-table"
				:headers="headersCommunicationsModal"
				:items="items"
				:server-items-length="items.length"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Nenhum programa com limite pré aprovado" />
				</template>

				<template v-slot:[`item.enabled`]="{ item }">
					<farm-row justify="center">
						<farm-switcher v-model="item.enabled" block />
					</farm-row>
				</template>
			</v-data-table>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				@onConfirm="$emit('onConfirm')"
				@onClose="$emit('onClose')"
				confirmLabel="Salvar"
			/>
		</template>
	</farm-modal>
</template>
<script>
import { defineComponent, toRefs } from 'vue';
import { headersCommunicationsModal } from '../../configurations/headers';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		items: {
			type: Array,
			required: true,
		},
		showMessage: {
			type: Boolean,
			required: true,
		},
	},
	setup(props) {
		const { value, items, showMessage } = toRefs(props);

		return {
			value,
			items,
			showMessage,
			headersCommunicationsModal,
		};
	},
});
</script>
<style lang="scss">
@import './ModalCommunications.scss';
</style>

