import { getters<PERSON>uilder } from '@farm-investimentos/front-mfe-libs-ts';

import stateBasicKeys from './_basicKeys';
import requestStatusKeys from './_requestStatusKeys';

export default {
	usersListResult: state => {
		if (!state.users) {
			return {
				items: [],
				totalPages: 0
			};
		}
		return {
			items: state.users.content.financialVehicleSettings.map((item, index) => {
				return {
					userName: item.userName.toUpperCase(),
					programName: item.financialVehicleSettings.filter(item => item.enabled).map(({ programName }) => programName.toUpperCase()).join(', '),
					id: index,
					userId: item.userId,
					data: item,
				};
			}),
			totalPages: state.users.totalPages
		};
	},
	usersOptionsListResult: state => {
		if (!state.usersOptions) {
			return [];
		}
		return state.usersOptions.map(item => {
			return {
				text: item.name.toUpperCase(),
				value: item.id
			};
		});
	},
	productsOptionsListResult: state => {
		if (!state.productsOptions) {
			return [];
		}
		return state.productsOptions.content.map(item => {
			return {
				text: item.name.toUpperCase(),
				value: item.id
			};
		});
	},
	communicationSettingsResult: state => {
		const { communicationSettings } = state;
		if (!communicationSettings) {
			return {
				items: [],
				totalPages: 0
			};
		}
		return {
			items: communicationSettings.content.communications.map((item, index) => {
				return {
					id: index,
					userName: item.user.name.toUpperCase(),
					bannners: item.communications.filter(item => item.enabled).map(({ type }) => type.toUpperCase()).join(', '),
					data: item.communications,
					userId: item.user.id
				};
			}),
			totalPages: communicationSettings.totalPages
		};
	},
	...gettersBuilder(stateBasicKeys),
	...gettersBuilder(requestStatusKeys),
};
