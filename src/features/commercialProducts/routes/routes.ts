import { EDIT, NEW } from '@/constants';

import CommercialProductHome from '../views/Home';
import CommercialProductCreateAndConfigurations from '../views/CreateAndConfigurations';
import CommercialProductAssociationWithFinancialVehicle from '../views/AssociationWithFinancialVehicle';

export const commercialProductsRoutes = [
	{
		path: 'produto_comercial',
		name: 'Produto_comercial',
		component: CommercialProductHome,
		meta: {
			title: 'Produtos Comerciais',
			icon: 'clipboard-text',
			roleKey: 'cadastros.produto_comercial',
		},
	},
	{
		path: 'produto_comercial/novo',
		name: 'Produto_comercial/novo',
		component: CommercialProductCreateAndConfigurations,
		meta: {
			title: 'Adicionar Produto Comercial',
			icon: 'folder-plus',
			roleKey: 'cadastros.produto_comercial',
		},
		props: { type: NEW },
	},
	{
		path: 'produto_comercial/:id/editar',
		name: 'Produto_comercial/edicao',
		component: CommercialProductCreateAndConfigurations,
		meta: {
			title: 'Editar Produto Comercial',
			icon: 'pencil',
			roleKey: 'cadastros.produto_comercial',
		},
		props: { type: EDIT },
	},
	{
		path: 'produto_comercial/:id/associacao_veiculo_financeiro',
		name: 'produto_comercial/associacao_veiculo_financeiro',
		component: CommercialProductAssociationWithFinancialVehicle,
		meta: {
			title: 'Editar Produto Comercial',
			icon: 'pencil',
			roleKey: 'cadastros.produto_comercial',
		},
	},
];
