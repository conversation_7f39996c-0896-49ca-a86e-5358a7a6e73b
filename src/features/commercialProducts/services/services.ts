import { queryString } from '@farm-investimentos/front-mfe-libs-ts';

import registerV3Service from '@/configurations/services/registerV3';

const client = registerV3Service;

export const getCommercialProducts = data => {
	const filters = queryString(data, {});
	const url = `/api/v1/commercial-product?${filters}`;
	return client.get(url);
};

export const getFilterCommercialProductTypes = () => {
	const url = `/api/v1/commercial-product-type`;
	return client.get(url);
};

export const getFilterCommercialProductLimits = () => {
	const url = `/api/v1/commercial-product-limit`;
	return client.get(url);
};

export const createCommercialProduct = payload => {
	const url = `/api/v1/commercial-product`;
	return client.post(url, payload);
};

export const editCommercialProduct = data => {
	const url = `/api/v1/commercial-product/${data.id}`;
	return client.patch(url, data.payload);
};

export const getCommercialProductById = data => {
	const url = `/api/v1/commercial-product/${data.id}`;
	return client.get(url);
};

export const getCommercialProductMobility = data => {
	const url = `/api/v1/commercial-product/${data.id}/operation-modality`;
	return client.get(url);
};

export const getCommercialProductFinancialVehicle = data => {
	const filters = queryString(data.filters, {});
	const url = `/api/v1/commercial-product/${data.id}/financial-vehicle?${filters}`;
	return client.get(url);
};

export const createAssociationCommercialProductFinancialVehicle = data => {
	const url = `/api/v1/commercial-product/${data.id}/financial-vehicle`;
	return client.post(url, data.payload);
};

export const getCommercialProductFinancialVehicleAvailable = data => {
	const url = `/api/v1/commercial-product/${data.id}/financial-vehicle/available`;
	return client.get(url);
};


export const getProvidersByFinancialVehicle = ({ id, params }) => {
	const url = `/api/v1/financial-vehicle/${id}/providers`;
	return client.get(url, { params });
};

export const getUnauthorizedProviders = (data: {
	commercialProductId: number;
	financialVehicleId: number;
}) => {
	const url = `/api/v1/commercial-product/${data.commercialProductId}/financial-vehicle/${data.financialVehicleId}/not-authorized-providers`;
	return client.get(url);
};

export const updateUnauthorizedProviders = (data) => {
	const url = `/api/v1/commercial-product/${data.commercialProductId}/financial-vehicle/${data.financialVehicleId}/not-authorized-providers`;
	return client.patch(url, { providerIds: data.providerIds });
};

export const associationFinancialVehicle = data => {
	const url = `/api/v1/commercial-product/${data.id}/financial-vehicle/${data.financialVehicleId}/enabled`;
	return client.patch(url, data.payload);
};

export const getFilterFinancialVehicle = () => {
	const url = `/api/v1/financial-vehicle-type`;
	return client.get(url);
};

export const getFilterSponsor = () => {
	const url = `/api/v1/product/sponsor`;
	return client.get(url);
};

export const addCardImage = data => {
	const url = `/api/v1/commercial-product/${data.id}/upload-card`;
	return client.post(url, data.payload);
};

export const removeCardImage = data => {
	const url = `/api/v1/commercial-product/${data.id}/delete-card`;
	return client.delete(url);
};
