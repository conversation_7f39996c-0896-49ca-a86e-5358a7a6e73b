export type NetWorhCardType = {
	id: number;
	name: string;
	type: string;
	document: string;
	initialNetWorth: string;
	currentNetWorth: string;
	realNetWorth: string;
};

type NetWorthMetaType = {
	createdAt: string;
	createdBy: string;
	updatedAt: string;
};

type NetWorthPaginationType = {
	page: number;
	size: number;
	totalItems: number;
	totalPages: number;
};

export type NetWorthListingType = NetWorthPaginationType & {
	content: NetWorhCardType[];
};

export type NetWorthFormTabQuotaTypes = {
	name: string;
	quantity: number;
	obs: string;
	value: number;
	quantityFix: number;
};

export type HistoryHeaderItem = {
	id: number;
	type: number;
	createdAt: string;
	createdBy: string;
};

type Quota = {
	name: string;
	quantity: number;
	obs: string;
	value: number;
	netWorth?: number;
};

export type NetWorthQuotaPayload = {
	type: number;
	dateOfOccurrence: string;
	quotas: Quota[];
};

type Quotas = {
	quotas: Quota[];
};

type NetWorthDetails = {
	jr: Quotas[];
	mz: Quotas[];
	sr: Quotas[];
	totalNetWorth: number;
	totalQuantity: number;
};

export type NetWorthDetailsType = {
	initialNetWorth: NetWorthDetails;
	currentNetWorth: NetWorthDetails;
	realNetWorth: NetWorthDetails;
	meta: NetWorthMetaType;
};

export const NetWorthFormTabModel: NetWorthFormTabQuotaTypes = {
	name: '',
	quantity: null,
	obs: '',
	value: null,
	quantityFix: 0,
};
