import { useGetter, useStore } from '@/composibles';
import { computed, ComputedRef } from 'vue';
import { NetWorthDetailsType, NetWorthListingType } from '../../types';

export default function useNetWorth() {
	const store = useStore();

	const getNetWorthList = params => {
		store.dispatch('netWorth/getNetWorthList', { params });
	};

	const listNetWorth: ComputedRef<NetWorthListingType> = computed(
		useGetter('netWorth', 'netWorthList')
	);
	const netWorthListRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthListRequestStatus')
	);

	const getFinancialVehicleTypes = () => {
		store.dispatch('cadastros/getFinancialVehicleTypes');
	};

	const financialVehicleTypes = computed(useGetter('cadastros', 'financialVehicleTypes'));

	const financialVehicleTypesRequestStatus = computed(
		useGetter('cadastros', 'financialVehicleTypesRequestStatus')
	);

	const getNetWorthDetails = (vehicleId: number): void => {
		store.dispatch('netWorth/getNetWorthDetails', { vehicleId });
	};

	const netWorthDetails: ComputedRef<NetWorthDetailsType> = computed(
		useGetter('netWorth', 'netWorthDetails')
	);

	const netWorthDetailsRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthDetailsRequestStatus')
	);

	const getNetWorthHistory = (vehicleId: number, params): void => {
		store.dispatch('netWorth/getNetWorthHistory', { vehicleId, params });
	};

	const getNetWorthHistoryUsers = (vehicleId: number) => {
		store.dispatch('netWorth/getNetWorthHistoryUsers', { vehicleId });
	};

	const netWorthHistoryUsers = computed(useGetter('netWorth', 'netWorthHistoryUsers'));

	const netWorthHistoryUsersRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthHistoryUsersRequestStatus')
	);

	const netWorthHistory = computed(useGetter('netWorth', 'netWorthHistory'));

	const netWorthHistoryRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthHistoryRequestStatus')
	);

	const postNetWorthIntegralization = (vehicleId, payload): void => {
		store.dispatch('netWorth/postNetWorthIntegralization', { vehicleId, payload });
	};
	const netWorthIntegralizationRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthIntegralizationRequestStatus')
	);

	const patchNetWorthAmortization = (vehicleId, payload): void => {
		store.dispatch('netWorth/patchNetWorthAmortization', { vehicleId, payload });
	};

	const netWorthAmortizationRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthAmortizationRequestStatus')
	);
	const getNetWorthEditDetails = (vehicleId: number) => {
		store.dispatch('netWorth/getNetWorthEditDetails', { vehicleId });
	};

	const netWorthEditDetails: ComputedRef<NetWorthDetailsType> = computed(
		useGetter('netWorth', 'netWorthEditDetails')
	);

	const netWorthEditDetailsRequestStatus: ComputedRef<string> = computed(
		useGetter('netWorth', 'netWorthEditDetailsRequestStatus')
	);

	return {
		listNetWorth,
		getNetWorthList,
		getNetWorthDetails,
		netWorthDetails,
		netWorthListRequestStatus,
		netWorthDetailsRequestStatus,
		getNetWorthHistory,
		patchNetWorthAmortization,
		netWorthHistory,
		netWorthHistoryRequestStatus,
		postNetWorthIntegralization,
		netWorthIntegralizationRequestStatus,
		netWorthAmortizationRequestStatus,
		netWorthEditDetailsRequestStatus,
		netWorthEditDetails,
		getNetWorthEditDetails,
		getNetWorthHistoryUsers,
		netWorthHistoryUsers,
		netWorthHistoryUsersRequestStatus,
		getFinancialVehicleTypes,
		financialVehicleTypes,
		financialVehicleTypesRequestStatus,
	};
}
