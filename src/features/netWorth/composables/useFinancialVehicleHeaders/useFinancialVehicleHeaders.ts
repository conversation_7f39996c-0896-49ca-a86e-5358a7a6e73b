import { computed, watch } from 'vue';
import { useGetter, useStore } from '@/composibles';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

export default function useFinancialVehicleHeaders(emit?) {
	const store = useStore();

	const getFinancialVehicleHeader = vehicleId => {
		store.dispatch('cadastros/getFinancialVehicleHeaders', { financialVehicleId: vehicleId });
	};
	const financialVehicleHeaderData = computed(
		useGetter('cadastros', 'financialVehicleHeaderData')
	);

	const financialVehicleHeaderRequestStatus = computed(
		useGetter('cadastros', 'financialVehicleHeaderRequestStatus')
	);

	const updatedHeaderHome = data => {
		emit('onUpdateHeaderForm', {
			title: data.name,
			listIcons: [data.id, data.type, ['', data.document, data.document]],
		});
	};
	watch(financialVehicleHeaderRequestStatus, newValue => {
		if (newValue === RequestStatusEnum.SUCCESS) {
			updatedHeaderHome(financialVehicleHeaderData.value.content!);
		}
	});

	return {
		getFinancialVehicleHeader,
		financialVehicleHeaderData,
		financialVehicleHeaderRequestStatus,
	};
}
