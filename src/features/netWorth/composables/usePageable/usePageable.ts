import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { ComputedRef, Ref, computed, ref, watch } from 'vue';
import { NetWorthListingType } from '../../types';

export type VuetifyTableSort = {
	field: string;
	descending: 'ASC' | 'DESC';
};

type UsePageable = {
	page: ComputedRef<number>;
	pagination: Ref<Pagination>;
	isFilterCounter: Ref<boolean>;
	isOpenFilter: Ref<boolean>;
	onChangePageLimit: (arg: number) => void;
	onChangePage: (arg: number) => void;
	onSortSelect: (arg: string) => void;
	onClickMainFilter: () => void;
	onInputChangeMainFilter: (value: string) => void;
	onApplyFilter: (arg: any) => void;
	onFiltersApplied: (arg: any) => void;
	httpStatus: ComputedRef<string>;
	isEmpty: Ref<boolean>;
};

type Sort = {
	orderby: string;
	order: string;
};

type PropsType = {
	calbackFn: (filters: any) => void;
	keyInputSearch: string;
	filters?: any;
	lowercaseSort?: boolean;
	sort?: Ref<Sort>;
	charInputSearch?: number;
	lazyFilters?: boolean;
};

export type Pagination = {
	page: number;
	limit: number;
	totalItems: number;
	totalPages: number;
};

export function usePageable(
	props: PropsType,
	paginationModel: Ref<Pagination>,
	httpStatus: ComputedRef<string>,
	httpResponsePagination: ComputedRef<NetWorthListingType>
): UsePageable {
	const canDoSearch = ref(true);
	const lazyFilters = ref(props.lazyFilters || false);
	const pagination = ref(paginationModel);
	const filterCurrent = ref(props.filters || null);
	const sortCurrent = ref(props.sort || null);
	const isOpenFilter = ref(false);
	const isFilterCounter = ref(false);
	const lowercaseSort = ref(props.lowercaseSort || false);
	const key = ref(props.keyInputSearch || 'search');
	const quantChar = ref(props.charInputSearch || 3);
	const isEmpty = ref(false);

	const page = computed(() => (pagination.value?.page || 0) + 1);

	function validParams(value = '') {
		const hasMainFilter = !!value;
		const hasSortValues =
			sortCurrent.value && Object.values(sortCurrent.value).filter(Boolean).length;
		const hasFilterValues =
			filterCurrent.value && Object.values(filterCurrent.value).filter(Boolean).length;

		return {
			page: pagination.value.page,
			limit: pagination.value.limit,
			...(hasFilterValues ? filterCurrent.value : {}),
			...(hasSortValues ? sortCurrent.value : {}),
			...(hasMainFilter ? { [key.value]: value } : {}),
		};
	}

	function onChangePageLimit(newPageLimit: number) {
		pagination.value.totalPages = Math.ceil(
			httpResponsePagination.value.content.length / newPageLimit
		);
		pagination.value.limit = newPageLimit;
		pagination.value.page = 0;

		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onChangePage(newPage: number): void {
		pagination.value.page = newPage - 1;
		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function getOrderByKey(): string {
		return lowercaseSort.value ? 'orderby' : 'orderBy';
	}

	function onSortSelect(value: string): void {
		const [orderby, order] = value.split('_');
		sortCurrent.value[getOrderByKey()] = orderby;
		sortCurrent.value.order = order;
		if (canDoSearch) {
			props.calbackFn(validParams());
		}
	}

	function onClickMainFilter(): void {
		isOpenFilter.value = !isOpenFilter.value;
	}

	function onApplyFilter(data): void {
		filterCurrent.value = {
			...data,
		};
		pagination.value.page = 0;
	}

	function onFiltersApplied(data): void {
		isFilterCounter.value = data;
	}

	function onInputChangeMainFilter(value: string): void {
		if (value.length >= quantChar.value || value === '') {
			if (canDoSearch) {
				props.calbackFn(validParams(value));
			}
		}
	}

	function generateResponsePagination() {
		pagination.value = {
			page: httpResponsePagination.value.page,
			limit: httpResponsePagination.value.size,
			totalItems: httpResponsePagination.value.totalItems,
			totalPages: httpResponsePagination.value.totalPages,
		};
	}

	function isEmptyRequest() {
		if (!httpResponsePagination.value.content.length) {
			isEmpty.value = true;
		} else {
			isEmpty.value = false;
		}
	}

	watch(httpStatus, newValue => {
		if (newValue === RequestStatusEnum.SUCCESS) {
			isEmptyRequest();
			generateResponsePagination();
		}
	});

	watch(props.filters, () => {
		pagination.value.page = 0;
		if (lazyFilters.value) return;
		props.calbackFn(validParams());
	});

	return {
		page,
		httpStatus,
		pagination,
		isOpenFilter,
		isFilterCounter,
		onChangePageLimit,
		onChangePage,
		onSortSelect,
		onClickMainFilter,
		onInputChangeMainFilter,
		onApplyFilter,
		onFiltersApplied,
		isEmpty,
	};
}
