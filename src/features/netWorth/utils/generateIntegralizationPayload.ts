export const generateIntegralizationPayload = (values, date) => {

	const dataValid = values
		.filter(x => x.quotas.length > 0)
		.map(x => ({
			...x,
			type: 1,
			dateOfOccurrence: date.value,
			quotas: x.quotas.map((item) => {
				let value = 0;
				if(typeof item.value === "number"){
					value = item.value;
				}else{
					value = item.value.replace('R$', '').replace('.', '').replace(',', '.');
				}
				if(item.name.length > 0){
					return {
						id: item.id ?? null,
						name: item.name.toUpperCase(),
						quantity: parseInt(item.quantityFix),
						obs: item.obs,
						value,
						isValid: item.name.length > 0
					};
				}
				return null;
			}),
		}));
	const payload = dataValid.filter((item) => {
		const dataMap = item.quotas.filter((quota)=> quota !== null);
		return dataMap.length > 0;
	});
	return { payload };
};
