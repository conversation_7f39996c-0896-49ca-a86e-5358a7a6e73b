export const generateAmortizationPayload = (values, date) => {
	const payload = values
		.filter(x => x.quotas.length > 0)
		.map(x => ({
			...x,
			type: 2,
			dateOfOccurrence: date.value,
			quotas: x.quotas.map((item)=>{
				let value = 0;

				if(typeof item.value === "number"){
					value = item.value.toString();
				}else{
					value = item.value.replace('R$', '').replace('.', '').replace(',', '.');
				}
				return{
					id: item.id ?? null,
					name: item.name.toUpperCase(),
					quantity: parseInt(item.quantityFix),
					obs: item.obs,
					value,
				};
			}),
		}));

	return { payload };
};
