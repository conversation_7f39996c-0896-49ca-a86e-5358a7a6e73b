<template>
	<farm-box>
		<farm-bodytext class="mb-4" :type="2" color="success"> {{ title }} </farm-bodytext>

		<farm-row class="mb-3">
			<farm-col
				:class="{ 'with-line': index !== 3 }"
				cols="3"
				v-for="(quota, index) in quotaTypes"
				:key="quota"
			>
				<div class="d-flex align-center">
					<farm-caption bold  variation="medium" ellipsis>
						Quantidade de Cotas {{ getLabelType(quota) }}:
					</farm-caption>
					<farm-caption variation="regular" ellipsis>
						{{
							plain
								? headerSummary[quota].quantity
								: headerSummary[quota].totalQuantity
						}}
					</farm-caption>
				</div>

				<div class="d-flex align-center py-1">
					<farm-caption bold class="mr-1"> PL {{ getLabelType(quota) }}:</farm-caption>

					<farm-caption variation="medium" ellipsis>
						{{
							plain
								? formatMoney(headerSummary[quota].netWorth)
								: formatMoney(headerSummary[quota].totalNetWorth)
						}}
					</farm-caption>
				</div>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { formatMoneyFourDecimal } from '@/helpers/masks';


export default defineComponent({
	props: {
		title: {
			type: String,
			required: true,
		},
		headerSummary: {
			type: Object,
			required: true,
		},
		plain: {
			type: Boolean,
			default: false,
		},
	},
	setup() {
		const quotaTypes = ['jr', 'mz', 'sr', 'unique'];

		function getLabelType(quotaType: string) {
			if(quotaType === 'unique') {
				return ' Única';
			}
			return quotaType.charAt(0).toUpperCase() + quotaType.slice(1);
		}

		function formatMoney(value){
			return formatMoneyFourDecimal(value);
		}

		return { formatMoney, getLabelType, quotaTypes };
	},
});
</script>
