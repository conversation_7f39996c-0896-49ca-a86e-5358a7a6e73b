<template>
	<farm-container>
		<farm-row justify="space-between" align="center">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Veículo Financeiro"
					:showFilters="isOpenFilter"
					@onInputChange="onInputChangeMainFilter"
					@onClick="onClickMainFilter"
				/>
			</farm-col>

			<farm-col cols="12" md="3" align="right">
				<farm-select-auto-complete
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortNetWorthOptions"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<NetWorthFilter v-show="isOpenFilter" @onApply="onApplyFilter" />
		</collapse-transition>

		<NetWorthDetailsModal
			v-if="!isLoading"
			:data="selectedModal"
			:isOpen="showModal"
			@onClose="handleModalState"
		/>

		<NetWorthList
			:data="listNetWorth.content"
			@edit="handleEdit"
			@history="handleHistory"
			@details="handleDetails"
		/>
		<farm-row extra-decrease v-if="pagination && !isEmpty">
			<farm-box>
				<farm-datatable-paginator
					:page="page"
					:total-pages="pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter, useIsLoading } from '@/composibles';

import { NetWorthList } from '../NetWorthList';
import { NetWorthFilter } from '../NetWorthFilter';
import { NetWorthDetailsModal } from '../NetWorthDetailsModal';
import { sortNetWorth as sortNetWorthOptions } from '../../configurations';
import { useNetWorth, usePageable } from '../../composables';

export default defineComponent({
	components: {
		NetWorthList,
		NetWorthFilter,
		NetWorthDetailsModal,
	},
	setup() {
		const {
			listNetWorth,
			getNetWorthList,
			netWorthListRequestStatus,
			netWorthDetails,
			getNetWorthDetails,
			netWorthDetailsRequestStatus,
		} = useNetWorth();

		const router = useRouter();
		const showModal = ref(false);

		const selectedModal = ref({
			productName: null,
			id: null,
			content: null,
		});

		const isLoading = useIsLoading([netWorthListRequestStatus, netWorthDetailsRequestStatus]);

		const paginationFilters = ref({
			totalItems: null,
			totalPages: null,
			limit: 10,
			page: 0,
		});

		const filters = ref({
			search: null,
			type: null,
		});

		const sortModel = ref('name_ASC');

		const sort = ref({
			orderby: 'name',
			order: 'ASC',
		});

		const handleHistory = data =>
			router.push(
				`/admin/cadastros/configuracoes/patrimonio_liquido/${data.id}/editar?path=history`
			);

		const handleDetails = data => {
			getNetWorthDetails(data.id);
			selectedModal.value.content = netWorthDetails;
			selectedModal.value.id = data.id;
			selectedModal.value.productName = data.name;
			showModal.value = !showModal.value;
		};

		const handleModalState = () => (showModal.value = !showModal.value);

		const handleEdit = data =>
			router.push(
				`/admin/cadastros/configuracoes/patrimonio_liquido/${data.id}/editar?path=integralizacao`
			);

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			onApplyFilter,
			isOpenFilter,
			onClickMainFilter,
			isEmpty,
		} = usePageable(
			{
				sort: sort,
				lowercaseSort: true,
				keyInputSearch: 'search',
				filters,
				charInputSearch: 2,
				calbackFn: filters => {
					getNetWorthList(filters);
				},
			},
			paginationFilters,
			netWorthListRequestStatus,
			listNetWorth
		);

		function load() {
			const initialFilters = { ...sort.value, ...paginationFilters.value };
			getNetWorthList(initialFilters);
		}

		onMounted(() => {
			load();
		});

		return {
			onInputChangeMainFilter,
			isLoading,
			filters,
			onChangePage,
			onChangePageLimit,
			sortNetWorthOptions,
			sortModel,
			isOpenFilter,
			onClickMainFilter,
			onSortSelect,
			onApplyFilter,
			handleHistory,
			handleDetails,
			handleEdit,
			showModal,
			selectedModal,
			handleModalState,
			listNetWorth,
			netWorthListRequestStatus,
			paginationFilters,
			pagination,
			page,
			isEmpty,
		};
	},
});
</script>

<style scoped lang="scss">
@import './Body.scss';
</style>
