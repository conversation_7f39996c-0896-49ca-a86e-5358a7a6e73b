<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col
				cols="12"
				sm="12"
				md="6"
				v-for="(item, index) in data"
				:key="`NetWorth-card-${item.id}-${index}`"
			>
				<NetWorthCard
					:data="item"
					@edit="handleEdit"
					@history="handleHistory"
					@details="handleDetails"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, toRefs } from 'vue';

import { NetWorthCard } from '../NetWorthCard';
import { NetWorhCardType } from '../../types';

export default defineComponent({
	components: {
		NetWorthCard,
	},
	props: {
		data: {
			type: Array as PropType<Array<NetWorhCardType>> | [],
			default: () => [],
		},
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);
		const isDataEmpty = computed(() => data.value.length === 0);

		const handleHistory = data => {
			emit('history', data);
		};
		const handleDetails = data => {
			emit('details', data);
		};

		const handleEdit = data => {
			emit('edit', data);
		};

		return { isDataEmpty, handleHistory, handleDetails, handleEdit };
	},
});
</script>
