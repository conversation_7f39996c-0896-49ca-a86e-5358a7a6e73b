<template>
	<farm-modal
		v-model="isOpen"
		v-if="data.content"
		persistent
		:offset-top="16"
		:offset-bottom="68"
	>
		<template #content>
			<farm-row>
				<farm-col cols="10">
					<farm-caption variation="semiBold">
						{{ data.productName }} | Detalhes
					</farm-caption>
				</farm-col>

				<farm-col cols="2" align="end">
					<farm-icon aria-label="Fechar" role="button" @click="$emit('onClose')">
						close
					</farm-icon>
				</farm-col>
				<farm-col cols="12" class="mb-6">
					<div class="d-flex align-center">
						<farm-bodytext :type="2" bold class="mr-1">
							Excesso de Spread:
						</farm-bodytext>
						<farm-bodytext :type="2" class="mr-1">
							{{ formatMoney(data.content.spreadExcess) }}</farm-bodytext
						>

						<farm-tooltip>
							<b>Excesso Spread</b>
							<br />
							Diferença entre o PL Real da Carteira Total e o PL Base Atual Total
							<template v-slot:activator>
								<farm-icon size="sm" color="gray">help-circle</farm-icon>
							</template>
						</farm-tooltip>
					</div>
				</farm-col>
			</farm-row>

			<template>
				<farm-collapsible custom-header custom-body title="">
					<template #header-content>
						<NetWorthDetailsModalHeader
							:title="'PL Base/Inicial'"
							:headerSummary="data.content.initialNetWorth"
						/>
					</template>
					<farm-row extraDecrease class="px-3 background">
						<farm-col
							v-for="quotaType in quotaTypes"
							:key="`quota-type-modal-details-${quotaType}`"
						>
							<ModalQuotaItem
								class="my-3"
								v-for="(quota, index) in getQuotas('initialNetWorth', quotaType)"
								:labelType="getLabelType(quotaType)"
								:key="'net-worth-modal-quota' + quotaType + index"
								:quota="quota"
							/>
						</farm-col>
					</farm-row>
				</farm-collapsible>

				<farm-collapsible class="my-4" custom-header custom-body title="">
					<template #header-content>
						<NetWorthDetailsModalHeader
							:title="'PL Base/Atual'"
							:headerSummary="data.content.currentNetWorth"
						/>
					</template>
					<farm-row extraDecrease class="px-3 background">
						<farm-col
							v-for="quotaType in quotaTypes"
							:key="`quota-type-modal-details-${quotaType}`"
						>
							<ModalQuotaItem
								v-for="(quota, index) in getQuotas('currentNetWorth', quotaType)"
								:labelType="getLabelType(quotaType)"
								class="my-3"
								:key="'net-worth-history-card-' + quotaType + index"
								:quota="quota"
								:index="index + 1"
							/>
						</farm-col>
					</farm-row>
				</farm-collapsible>

				<farm-collapsible class="my-4" custom-header custom-body title="" plain>
					<template #header-content>
						<NetWorthDetailsModalHeader
							:title="'PL Real da Carteira'"
							:headerSummary="data.content.realNetWorth"
							plain
						/>
					</template>
				</farm-collapsible>
			</template>
		</template>
		<template #footer>
			<farm-line noSpacing />
			<div class="d-flex align-center justify-end pa-4">
				<farm-btn @click="$emit('onClose')"> Fechar </farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { brl as formatMoney } from '@farm-investimentos/front-mfe-libs-ts';
import { defineComponent } from 'vue';
import { ModalQuotaItem } from '../ModalQuotaItem';
import { NetWorthDetailsModalHeader } from '../NetWorthDetailsModalHeader';

type NetWorthType = 'currentNetWorth' | 'initialNetWorth' | 'realNetWorth';
type QuotaType = 'sr' | 'jr' | 'mz' | 'unique';

export default defineComponent({
	components: {
		ModalQuotaItem,
		NetWorthDetailsModalHeader,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
		isOpen: {
			type: Boolean,
			default: false,
		},
	},
	setup({ data }) {
		function getQuotas(type: NetWorthType, quota: QuotaType) {
			return data.content[type][quota].quotas;
		}
		function getLabelType(quotaType: string) {
			if(quotaType === 'unique'){
				return 'Única';
			}
			return quotaType.charAt(0).toUpperCase() + quotaType.slice(1);
		}
		const quotaTypes: QuotaType[] = ['jr', 'mz', 'sr', 'unique'];
		return { formatMoney, getQuotas, getLabelType, quotaTypes };
	},
});
</script>

<style lang="scss">
@import './NetWorthDetailsModal.scss';
</style>
