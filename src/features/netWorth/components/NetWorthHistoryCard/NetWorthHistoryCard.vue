<template>
	<farm-box>
		<farm-card class="mb-4">
			<farm-card-content>
				<farm-row no-default-gutters>
					<farm-bodytext variation="bold" :type="1"> Tipo: </farm-bodytext>
					<farm-bodytext :color="getHeaders(items.type).color" class="mx-2" bold>
						{{ getHeaders(items.type).label }}
					</farm-bodytext>
					<farm-icon :color="getHeaders(items.type).color" size="md">
						{{ getHeaders(items.type).icon }}
					</farm-icon>
				</farm-row>
				<CardListTextHeader noSpacing class="mt-2" :data="headers" />
			</farm-card-content>

			<farm-line no-spacing />

			<farm-card-content background="base">
				<div v-for="quotaKey in quotaTypes" :key="'L=quotaTypes::key=' + quotaKey">
					<farm-bodytext bold v-if="items.quotas[quotaKey].length"
						>Cotas {{ getLabelType(quotaKey) }}</farm-bodytext
					>
					<HistoryQuotaItem
						class="my-3"
						v-for="(quotaValue, index) in getQuotas(quotaKey)"
						:key="`${quotaKey}::${index}`"
						:quota="quotaValue"
						:labelType="getLabelType(quotaKey)"
						:type="items.type"
					/>
				</div>

				<farm-row>
					<farm-col class="with-line">
						<farm-caption bold>Quantidade Total de Cotas</farm-caption>
						<farm-bodytext bold color="success">
							{{ items.totalQuotaQuantity }}
						</farm-bodytext>
					</farm-col>
					<farm-col>
						<farm-caption bold>PL Base Total</farm-caption>
						<farm-bodytext bold color="success">{{
							formatMoney(items.totalBaseQuotas)
						}}</farm-bodytext>
					</farm-col>
				</farm-row>
			</farm-card-content>
		</farm-card>
	</farm-box>
</template>

<script lang="ts">
import CardListTextHeader from '@/components/CardListTextHeader';
import { CardListTextType } from '@/components/CardListTextHeader/types';
import { brl as formatMoney } from '@farm-investimentos/front-mfe-libs-ts';
import { PropType, defineComponent, toRefs } from 'vue';
import { headerTypes } from '../../configurations';
import { HistoryQuotaItem } from '../HistoryQuotaItem';

type QuotaType = 'sr' | 'jr' | 'mz' | 'unique';

export default defineComponent({
	components: { CardListTextHeader, HistoryQuotaItem },
	props: {
		items: {
			type: Object,
			required: true,
		},
		headers: {
			type: Array as PropType<CardListTextType[]>,
			required: true,
		},
	},
	setup(props) {
		const { items } = toRefs(props);

		const getHeaders = <T extends keyof typeof headerTypes>(key: T) => {
			return headerTypes[key];
		};
		const quotaTypes: QuotaType[] = ['jr', 'mz', 'sr', 'unique'];

		function getQuotas(quota: QuotaType) {
			return items.value.quotas[quota];
		}

		function getLabelType(quotaType: string) {
			if(quotaType === 'unique') {
				return 'Única';
			}
			return quotaType.charAt(0).toUpperCase() + quotaType.slice(1);
		}

		return {
			formatMoney,
			headerTypes,
			getHeaders,
			quotaTypes,
			getQuotas,
			getLabelType,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './NetWorthHistoryCard.scss';
</style>
