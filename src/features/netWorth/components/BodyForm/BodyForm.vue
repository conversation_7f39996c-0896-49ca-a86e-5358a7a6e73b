<template>
	<farm-container>
		<HeaderForm :data="dataHeaderForm" />
		<TabsForm
			:tabList="tabs"
			:valueDefault="DATA"
			:isEdit="isEdit"
			@onUpdateCurrentTab="onUpdateCurrentTab"
		/>

		<TabIntegralization
			v-if="isTabIntegralizacao"
			:isEdit="isEdit"
			:label="'Integralização'"
			@onSubmit="onSubmit"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>

		<TabAmortization
			v-if="isTabAmortizacao"
			:label="'Amortização'"
			@onSubmit="onSubmit"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>

		<TabHistory
			v-if="isTabHistory"
			:label="'Histórico'"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>

		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:hiddenButton="isHiddenButton"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import TabsForm, { TabsFormTypes } from '@/components/TabsForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { DATA } from '@/constants';

import { TabIntegralization } from '../TabIntegralization';
import { TabAmortization } from '../TabAmortization';
import { TabHistory } from '../TabHistory';
import { tabDefault, tabEdit } from '../../configurations/tabs';
import { WALLET } from '../../constants';
import { headerPage } from '../../configurations';

export default defineComponent({
	components: {
		TabsForm,
		FooterForm,
		HeaderForm,
		TabIntegralization,
		TabAmortization,
		TabHistory,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: null,
			tabs: tabDefault,
			currentTab: DATA,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			DATA,
			WALLET,
			labelButton: 'Cadastrar',
			dispatchSubmit: null,
			headerPage,
		};
	},
	computed: {
		isEdit(): boolean {
			return true;
		},
		isTabIntegralizacao(): boolean {
			return this.currentTab === 'integralizacao';
		},
		isTabAmortizacao(): boolean {
			return this.currentTab === 'amortizacao';
		},
		isTabHistory(): boolean {
			return this.currentTab === 'history';
		},
		isHiddenButton(): boolean {
			return this.currentTab === 'history';
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	methods: {
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			this.tabs = tabEdit;
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/configuracoes/patrimonio_liquido`,
			});
		},
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onUpdateCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
			this.disabledButtonFooter = false;
		},
		onUpdateHeaderForm(data: HeaderFormTypes): void {
			this.dataHeaderForm = parseDataHeader(data, headerPage);
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
	},
});
</script>
