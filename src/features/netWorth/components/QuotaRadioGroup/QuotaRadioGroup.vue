<template>
	<farm-radio-group class="d-flex align-center items-center" v-model="value" size="lg">
		<farm-radio v-model="radioValue" value="1" size="md" />
		<farm-label class="mr-2 ml-2 mb-0">Cota JR</farm-label>

		<farm-radio v-model="radioValue" value="2" size="md" />
		<farm-label class="mr-2 ml-2 mb-0">Cota MZ</farm-label>

		<farm-radio v-model="radioValue" value="3" size="md" />
		<farm-label class="mr-2 ml-2 mb-0">Cota SR</farm-label>

		<farm-radio v-model="radioValue" value="4" size="md" />
		<farm-label class="mr-2 ml-2 mb-0">Cota Única</farm-label>
	</farm-radio-group>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
export default defineComponent({
	props: {
		value: {
			type: Number,
			required: true,
		},
	},
	setup(props, { emit }) {
		const radioValue = computed({
			get() {
				return props.value;
			},
			set(newValue) {
				return emit('radioGroupVal', +newValue);
			},
		});

		return { radioValue };
	},
});
</script>
