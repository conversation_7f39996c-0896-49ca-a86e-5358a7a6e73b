<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" class="mt-3 mb-4">
				<farm-idcaption icon="minus-circle-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6">
							{{ label }}
						</farm-heading>
					</template>
					<template v-slot:subtitle>
						Realize a redução nos valores das cotas.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<QuotaRadioGroup v-model="subType" class="py-4" @radioGroupVal="handleSubType" />
		<template v-for="(item, i) in form">
			<farm-form
				v-model="formStatus[getTypeKey(item.subType).toLowerCase()].status"
				:ref="e => (formRef[item.subType] = e)"
				:key="i"
				v-show="subType === item.subType"
			>
				<farm-row v-if="item.quotas.length > 0">
					<farm-col cols="12" md="4">
						<farm-label for="form-product-data-start-date" required>
							Data de Ocorrência
						</farm-label>
						<farm-input-datepicker
							ref="datepickerdateOfOccurrence"
							inputId="form-networth-date-of-occurrence"
							v-model="dateOfOccurrence"
							:required="true"
							:rules="currentForm.quotas.length ? [rules.required] : []"
						/>
					</farm-col>
				</farm-row>
				<farm-row extra-decrease class="pb-4" v-if="item.quotas.length > 0">
					<farm-line noSpacing />
				</farm-row>
				<farm-row v-for="(quota, index) in item.quotas" :key="index">
					<farm-col>
						<FormAmortization
							:data="quota"
							:type="getTypeLabel(item.subType)"
							@validForm="validForm"
						/>
						<farm-row
							extra-decrease
							:class="[i + 1 == currentForm.quotas.length ? 'pb-0' : 'pb-4']"
						>
							<farm-line noSpacing />
						</farm-row>
					</farm-col>
				</farm-row>
				<farm-row extra-decrease v-if="item.quotas.length === 0">
					<farm-box>
						<farm-emptywrapper :subtitle="subtitle"/>
					</farm-box>
				</farm-row>
			</farm-form>
		</template>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { useIsLoading, useRoute, useRouter } from '@/composibles';
import { format } from '@/helpers/formatUpdateUser';
import { cleanMaskMoney } from '@/helpers/masks';

import { useFinancialVehicleHeaders, useForm, useNetWorth } from '../../composables';
import { generateAmortizationPayload, getTypeLabel, getTypeKey } from '../../utils';
import { FormAmortization } from '../FormAmortization';
import { QuotaRadioGroup } from '../QuotaRadioGroup';

export default defineComponent({
	props: {
		isEdit: {
			type: Boolean,
		},
		label: {
			type: String,
		},
	},
	components: {
		FormAmortization,
		QuotaRadioGroup,
	},
	setup(_, { emit }) {
		const router = useRouter();
		const route = useRoute();
		const {
			form,
			formRef,
			formStatus,
			isAllFormsValid
		} = useForm();
		const {
			patchNetWorthAmortization,
			netWorthAmortizationRequestStatus,
			netWorthDetailsRequestStatus,
			getNetWorthEditDetails,
			netWorthEditDetails,
			netWorthEditDetailsRequestStatus,
		} = useNetWorth();
		const {
			getFinancialVehicleHeader,
			financialVehicleHeaderRequestStatus
		} = useFinancialVehicleHeaders(emit);

		const isLoading = useIsLoading([
			financialVehicleHeaderRequestStatus,
			netWorthAmortizationRequestStatus,
			netWorthDetailsRequestStatus,
			netWorthEditDetailsRequestStatus,
		]);

		const dateOfOccurrence = ref('');
		const valid = ref(false);
		const formIsValid = ref(false);
		const rules = ref({
			required: value => !!value || 'Campo obrigatório',
		});
		const subType = ref(1);

		const currentForm = computed(() => form.value[subType.value - 1]);
		const subtitle = computed(() => {
			return `Tente incluir uma nova integralização ${getTypeLabel(subType.value)}.`;
		});
		const vehicleId = computed(() => route.params.vehicleId);

		function onSubmit(): void {
			const { payload } = generateAmortizationPayload(form.value, dateOfOccurrence);
			patchNetWorthAmortization(vehicleId.value, payload);
		}

		function load(): void {
			getFinancialVehicleHeader(vehicleId.value);
			getNetWorthEditDetails(vehicleId.value);
			emit('onSubmit', onSubmit);
		}

		function updateFooterHome(data): void {
			const filterData = { ...data, createdAt: null, createdBy: null };
			const updatedData = format(filterData);
			emit('onUpdateFooterFormData', updatedData);
		}

		function formatQuotas(quotaType = 'jr') {
			const key = quotaType === 'única' ? 'unique' : quotaType;
			const quotas = netWorthEditDetails.value[key].map((item) => {
				return {
					...item,
					quantityFix: item.quantityFix || 0,
					initialNetWorth: (item.quantity - (item.quantityFix || 0)) * parseFloat(item.value),
					value: parseFloat(item.value)
				};
			});
			currentForm.value.quotas = [...quotas];
		}

		function createModelValid(size: number) {
			const dataValid = [];
			for(let i=0; i < size; i++){
				dataValid.push(false);
			}
			return [...dataValid];
		}

		function isValidFieldValue(value, valueDataInput): boolean {
			if(!value) return true;
			if(value.length === 2) {
				return true;
			}
			const valueWithoutMask = parseFloat(value.toString().replace('R$', ''));
			if(valueWithoutMask === 0){
				return true;
			}
			if(cleanMaskMoney(value) > cleanMaskMoney(valueDataInput)){
				return true;
			}
			return false;
		}

		function validFieldsForm(quotas, valid) {
			for(let i=0; i < quotas.length; i++){
				if(quotas[i].name.length === 0){
					valid[i] = false;
					break;
				}
				if(quotas[i].quantityFix.toString().length === 0){
					valid[i] = false;
					break;
				}
				if(isValidFieldValue(quotas[i].value, quotas[i].valueDataInput)){
					valid[i] = false;
					break;
				}
				if(quotas[i].initialNetWorth.toString().length === 0){
					valid[i] = false;
					break;
				}
				valid[i] = true;
			}
			const hasFieldValid = valid.filter((item) => {
				return item === false;
			});
			return hasFieldValid.length === 0;
		}

		function validForm(): void {
			if(currentForm.value.quotas.length === 0) {
				emit('onDisabledButtonFooter', false);
				return;
			}
			if(dateOfOccurrence.value.length === 0){
				emit('onDisabledButtonFooter', false);
				return;
			}
			const dataCurrentValid = createModelValid(currentForm.value.quotas.length);
			const isValid = validFieldsForm(currentForm.value.quotas, dataCurrentValid);
			emit('onDisabledButtonFooter', isValid);
		}

		function handleSubType(val): void {
			subType.value = val;
			validForm();
		}

		watch(
			() => form.value[subType.value],
			() => {
				const quotaType = getTypeLabel(subType.value).toLowerCase();
				formatQuotas(quotaType);
			}
		);
		watch(netWorthEditDetailsRequestStatus, newValue => {
			if (newValue == RequestStatusEnum.SUCCESS) {
				formatQuotas();
				updateFooterHome(netWorthEditDetails.value.meta);
			}
		});
		watch(netWorthAmortizationRequestStatus, newValue => {
			if (newValue == RequestStatusEnum.SUCCESS) {
				notification(RequestStatusEnum.SUCCESS, 'Amortização Realizada com sucesso!');
				setTimeout(() => {
					router.go(0);
				}, 1000);
			}
		});
		watch(currentForm.value, ()=>{
			validForm();
		});
		watch(dateOfOccurrence, (newValue) => {
			if(newValue){
				validForm();
				return;
			}
			emit('onDisabledButtonFooter', false);
		});

		onMounted(() => {
			load();
		});

		return {
			form,
			valid,
			rules,
			updateFooterHome,
			getTypeLabel,
			getTypeKey,
			isLoading,
			formIsValid,
			formRef,
			formStatus,
			handleSubType,
			dateOfOccurrence,
			isAllFormsValid,
			currentForm,
			subType,
			subtitle,
			validForm
		};
	},
});
</script>
