<template>
	<farm-box>
		<farm-form class="mb-6" v-model="valid" v-if="!isError">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-type">Tipo</farm-label>

					<farm-select-auto-complete
						id="form-filtro-type"
						v-model="filters.type"
						:items="[
							{ id: 2, label: 'Amortização' },
							{ id: 1, label: 'Integralização' },
						]"
						item-value="id"
						item-text="label"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-dateOfOccurrence">Data</farm-label>

					<farm-input-datepicker
						ref="dateOfOccurrence"
						inputId="form-filtro-dateOfOccurrence"
						v-model="filters.dateOfOccurrence"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-user">Usuário</farm-label>
					<farm-select-auto-complete
						id="form-filtro-user"
						v-model="filters.user"
						:items="handleUsers"
						item-text="label"
						item-value="id"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-quota">Quantidade Total de Cotas</farm-label>
					<farm-select-auto-complete
						id="form-filtro-total-quota"
						v-model="rangeFilters.totalQuotaQuantity"
						:items="filterQuotaQuantity"
						item-text="label"
						item-value="id"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-baseQuotaValue">PL Base Total</farm-label>
					<farm-select-auto-complete
						id="form-filtro-baseQuotaValue"
						v-model="rangeFilters.baseQuotaValue"
						:items="filterBaseValues"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col class="mt-8" md="3">
					<farm-btn-confirm outlined title="Aplicar Filtros" @click="apply">
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="ml-0 ml-sm-2"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="load" />
		</div>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>
<script lang="ts">
import { useIsLoading, useRoute } from '@/composibles';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { computed, defineComponent, onMounted, ref } from 'vue';
import { useNetWorth } from '../../composables';
import { filterBaseValues, filterQuotaQuantity } from '../../configurations/filterValues';
import { buildFilterRangeValue } from '../../utils/buildFilterRangeValue';

export default defineComponent({
	setup(_, { emit }) {
		const route = useRoute();

		const filters = ref<{ type: string; dateOfOccurrence: string; user: string }>({
			type: null,
			dateOfOccurrence: null,
			user: null,
		});
		const rangeFilters = ref<{
			baseQuotaValue: number[];
			totalQuotaQuantity: number[];
		}>({
			baseQuotaValue: null,
			totalQuotaQuantity: null,
		});
		const valid = ref(false);

		onMounted(() => {
			load();
		});

		const { getNetWorthHistoryUsers, netWorthHistoryUsers, netWorthHistoryUsersRequestStatus } =
			useNetWorth();

		const isLoading = useIsLoading([netWorthHistoryUsersRequestStatus]);

		const vehicleId = computed(() => route.params.vehicleId);

		const isError = computed(() => {
			const values = [netWorthHistoryUsersRequestStatus.value];
			return values.includes(RequestStatusEnum.ERROR);
		});

		const handleUsers = computed(() =>
			netWorthHistoryUsers.value?.map(x => ({ id: x, label: x }))
		);

		function apply() {
			const formattedRangeFilters = buildFilterRangeValue(rangeFilters.value);

			const formattedFilters = Object.entries(filters.value).reduce((acc, [key, value]) => {
				acc[key] = value === '' ? null : value;
				return acc;
			}, {});

			const filtersPayload = Object.assign({}, formattedFilters, ...formattedRangeFilters);

			emit('onApply', filtersPayload);
		}

		function onFilterClear() {
			filters.value = { type: null, dateOfOccurrence: null, user: null };
			rangeFilters.value = { baseQuotaValue: null, totalQuotaQuantity: null };
			emit('onApply', {});
		}

		function load() {
			getNetWorthHistoryUsers(vehicleId.value);
		}

		return {
			valid,
			filters,
			apply,
			onFilterClear,
			filterQuotaQuantity,
			filterBaseValues,
			rangeFilters,
			isError,
			load,
			isLoading,
			handleUsers,
		};
	},
});
</script>
