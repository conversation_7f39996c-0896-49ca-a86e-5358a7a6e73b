<template>
	<farm-row class="flex-column" :class="{ 'with-line': labelType !== 'Sr' }">
		<farm-col class="d-flex align-center">
			<farm-caption bold class="mr-1">
				Quantidade de Cotas {{ labelType }} {{ quota.name }} :
			</farm-caption>
			{{ quota.quantity }}
		</farm-col>

		<farm-col class="d-flex align-center py-2">
			<farm-caption
				bold
				class="mr-1">
				<PERSON><PERSON> <PERSON> Cota {{ labelType }} {{ quota.name }}:
			</farm-caption>
			<farm-caption variation="medium">
				{{ formatMoneyFourDecimal(cleanMaskMoney(quota.value)) }}
			</farm-caption>
		</farm-col>

		<farm-col class="d-flex align-center">
			<farm-caption bold class="mr-1">
				PL {{ labelType }} {{ quota.name }}:
			</farm-caption>
			<farm-caption variation="medium">
				{{ formatMoneyFourDecimal(cleanMaskMoney(quota.netWorth)) }}
			</farm-caption>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';
import { formatMoneyFourDecimal, cleanMaskMoney } from '@/helpers/masks';

export default defineComponent({
	components: { CardListTextHeader },
	props: {
		quota: {
			type: Object,
			required: true,
		},
		labelType: {
			type: String,
			required: true,
		},
		index: {
			type: Number,
		},
	},
	setup() {
		return {
			formatMoneyFourDecimal,
			cleanMaskMoney
		};
	},
});
</script>

<style lang="scss">
@import './ModalQuotaItem.scss';
</style>
