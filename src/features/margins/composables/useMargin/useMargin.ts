import { computed, ref } from 'vue';

import { exportHandler } from '@farm-investimentos/front-mfe-libs-ts';

import { useGetter, useIsLoading, useStore } from '@/composibles';


import type {
	GetCampaignMarginDetailRequest,
	GetCampaignMarginHistoryListRequest,
	GetCampaignMarginListRequest,
	GetCommercialProductsListRequest,
	ImportCampaignMarginListRequest,
} from '../../services/types';

const modals = ref({
	import: {
		status: false,
	},
	history: {
		status: false,
		campaignId: null,
		commercialProductId: null,
		productId: null,
	},
	detail: {
		status: false,
		campaignId: null,
		commercialProductId: null,
		productId: null,
	},
});

export default function useMargin() {
	const store = useStore();

	const campaignMarginList = computed(useGetter('margins', 'campaignMarginList'));
	const campaignMarginListRequestStatus = computed(
		useGetter('margins', 'campaignMarginListRequestStatus')
	);
	const campaignMarginListPageable = computed(useGetter('margins', 'campaignMarginListPageable'));
	const campaignMarginListAlert = computed(useGetter('margins', 'campaignMarginListAlert'));

	const commercialProductsList = computed(useGetter('margins', 'commercialProductsList'));
	const commercialProductsListRequestStatus = computed(
		useGetter('margins', 'commercialProductsListRequestStatus')
	);



	const campaignMarginDetail = computed(useGetter('margins', 'campaignMarginDetail'));
	const campaignMarginDetailRequestStatus = computed(
		useGetter('margins', 'campaignMarginDetailRequestStatus')
	);
	const campaignMarginHistoryList = computed(useGetter('margins', 'campaignMarginHistoryList'));
	const campaignMarginHistoryListRequestStatus = computed(
		useGetter('margins', 'campaignMarginHistoryListRequestStatus')
	);
	const campaignMarginHistoryListPageable = computed(
		useGetter('margins', 'campaignMarginHistoryListPageable')
	);
	const importCampaignMarginDataRequestStatus = computed(
		useGetter('margins', 'importCampaignMarginDataRequestStatus')
	);

	const isLoading = useIsLoading([
		campaignMarginListRequestStatus,
		campaignMarginDetailRequestStatus,
		campaignMarginHistoryListRequestStatus,
		importCampaignMarginDataRequestStatus,
	]);

	const isLoadingCommercialProductsList = useIsLoading([commercialProductsListRequestStatus]);
	const fetchMarginList = (request: GetCampaignMarginListRequest) => {
		store.dispatch('margins/getCampaignMarginList', request);
	};
	const fetchCommercialProductsList = (request: GetCommercialProductsListRequest) => {
		store.dispatch('margins/getCommercialProductsList', request);
	};
	const fetchMarginDetail = (request: GetCampaignMarginDetailRequest) => {
		store.dispatch('margins/getCampaignMarginDetail', request);
	};
	const fetchMarginHistory = (request: GetCampaignMarginHistoryListRequest) => {
		store.dispatch('margins/getCampaignMarginHistoryList', request);
	};
	const openModal = (
		type: keyof typeof modals.value,
		properties?: Omit<(typeof modals.value)[typeof type], 'status'>
	) => {
		const modal = modals.value[type];

		modal.status = true;

		if (!properties) {
			return;
		}

		Object.entries(properties).forEach(([key, element]) => {
			modal[key] = element;
		});
	};
	const closeModal = (type: string) => {
		const modal = modals.value[type];

		modal.status = false;

		Object.keys(modal).forEach(key => {
			if (key !== 'status') {
				modal[key] = null;
			}
		});
	};
	const handleImport = (request: ImportCampaignMarginListRequest) => {
		store.dispatch('margins/importCampaignMargin', request);
	};
	const handleExport = () => {
		exportHandler.methods.exportHandler(() =>
			store.dispatch('margins/exportCampaignMarginList')
		);
	};

	const fetchMarginSpreadsheetModel = () => {
		exportHandler.methods.exportHandler(() =>
			store.dispatch('margins/exportCampaignMarginSpreadsheet')
		);
	};

	return {
		commercialProductsList,
		commercialProductsListRequestStatus,
		campaignMarginList,
		campaignMarginListRequestStatus,
		campaignMarginListPageable,
		campaignMarginListAlert,
		campaignMarginDetail,
		campaignMarginDetailRequestStatus,
		campaignMarginHistoryList,
		campaignMarginHistoryListPageable,
		campaignMarginHistoryListRequestStatus,
		isLoading,
		isLoadingCommercialProductsList,
		modals,
		openModal,
		closeModal,
		handleImport,
		handleExport,
		fetchMarginDetail,
		fetchCommercialProductsList,
		fetchMarginList,
		fetchMarginHistory,
		fetchMarginSpreadsheetModel,
		importCampaignMarginDataRequestStatus,
	};
}
