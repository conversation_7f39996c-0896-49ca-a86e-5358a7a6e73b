<template>
	<farm-modal
		class="margin-detail-modal"
		:value="value"
		:offset-top="52"
		:offset-bottom="68"
		@input="$emit('on-close')"
	>
		<template #header>
			<farm-dialog-header title="Detalhes" @onClose="$emit('on-close')" />
		</template>

		<template #content v-if="!isLoading">
			<MarginDetailCard
				v-if="campaignMarginDetail && !isError"
				:campaign-margin-detail="campaignMarginDetail"
			/>
			<template v-else>
				<farm-alert-reload
					label="Um erro ocorreu ao buscar os detalhes"
					@onClick="fetchWithParameters"
				/>
			</template>
		</template>

		<template #footer>
			<footer class="d-flex align-center justify-end pa-4">
				<farm-btn @click="$emit('on-close')">Fechar</farm-btn>
			</footer>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, computed } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { OPERATION_TYPES_ENUM } from '@/constants';

import MarginDetailCard from '../MarginDetailCard';
import { useMargin } from '../../composables';
import { GetCampaignMarginDetailRequest } from '../../services/types';

export default defineComponent({
	components: {
		MarginDetailCard,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {
		const {
			modals,
			campaignMarginDetail,
			campaignMarginDetailRequestStatus,
			isLoading,
			fetchMarginDetail,
		} = useMargin();

		const request: GetCampaignMarginDetailRequest = {
			params: {
				campaign_id: modals.value.detail.campaignId,
			},
			query: {
				commercial_product_id: modals.value.detail.commercialProductId,
				product_id: modals.value.detail.productId,
			},
		};

		const isError = computed(() => campaignMarginDetailRequestStatus.value?.type === 'ERROR');
		const operationsToShow = computed(() =>
			campaignMarginDetail.value?.marginOperations?.filter(
				operation => !!operation.campaignOperationId
			)
		);

		const fetchWithParameters = () => fetchMarginDetail(request);

		onBeforeMount(() => fetchWithParameters());

		return {
			OPERATION_TYPES_ENUM,
			isError,
			campaignMarginDetail,
			isLoading,
			operationsToShow,
			fetchWithParameters,
			defaultDateFormat,
		};
	},
});
</script>
