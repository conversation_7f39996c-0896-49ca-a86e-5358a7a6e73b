<template>
	<farm-form class="mb-6" ref="filterFormComponent">
		<farm-row>
			<farm-col md="6">
				<farm-form-mainfilter
					label="Buscar Produto"
					:show-filters="isShowingFilters"
					@onClick="toggleFilters"
					@onInputChange="updateNameFilter"
				/>
			</farm-col>

			<slot name="actions" />
		</farm-row>
		<farm-row v-show="isShowingFilters">
			<farm-col
				cols="12"
				md="3"
				:class="{ 'd-flex justify-center align-center': isLoadingCommercialProductsList }"
			>
				<farm-loader v-if="isLoadingCommercialProductsList" size="small" />
				<template v-else>
					<farm-label>Produto Comercial</farm-label>
					<farm-select
						v-model="formFilters.commercialProductId"
						item-value="id"
						item-text="name"
						:items="commercialProductsList"
					/>
				</template>
			</farm-col>
			<farm-col cols="12" md="3">
				<farm-label>Status Produto Comercial</farm-label>
				<farm-select
					v-model="formFilters.campaignCommercialProductStatus"
					:items="FORM_FILTER_STATUSES"
				/>
			</farm-col>
			<farm-col cols="12" md="3">
				<farm-label>Margem Comercial</farm-label>
				<farm-select v-model="formFilters.hasMargin" :items="FORM_FILTER_MARGINS" />
			</farm-col>

			<farm-col cols="12">
				<farm-btn outlined @click="updateListFilters"> Aplicar Filtros</farm-btn>
				<farm-btn plain @click="clearFilters">Limpar Filtros</farm-btn>
			</farm-col>
		</farm-row>
	</farm-form>
</template>

<script lang="ts">
import { defineComponent, watch } from 'vue';

import { FORM_FILTER_STATUSES, FORM_FILTER_MARGINS } from '../../constants';
import { useMargin, useMarginListFilters } from '../../composables';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	setup() {
		const {
			commercialProductsList,
			commercialProductsListRequestStatus,
			isLoadingCommercialProductsList,
			fetchMarginList,
		} =
			useMargin();
		const {
			filters: campaignFilters,
			formFilters,
			isShowingFilters,
			filterFormComponent,
			toggleFilters,
			clearFilters,
			updateNameFilter,
			updateListFilters,
			filterByProductsWithNoMargin,
		} = useMarginListFilters(fetchMarginList);


		watch(commercialProductsListRequestStatus, newValue => {
			if (newValue === RequestStatusEnum.SUCCESS) {
				filterFormComponent.value.restart();
			}
		});

		return {
			FORM_FILTER_STATUSES,
			FORM_FILTER_MARGINS,
			campaignFilters,
			formFilters,
			commercialProductsList,
			isShowingFilters,
			filterFormComponent,
			isLoadingCommercialProductsList,
			toggleFilters,
			clearFilters,
			updateNameFilter,
			updateListFilters,
			filterByProductsWithNoMargin,
		};
	},
});
</script>
