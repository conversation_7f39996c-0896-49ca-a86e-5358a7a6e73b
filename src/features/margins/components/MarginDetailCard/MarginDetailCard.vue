<template>
	<div>
		<dl>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1"> Produto: </farm-caption>
				<farm-caption tag="dd">{{ campaignMarginDetail.productName }}</farm-caption>
			</div>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1">
					Produto Comercial:
				</farm-caption>
				<farm-caption tag="dd">{{ campaignMarginDetail.commercialProductName }}</farm-caption>
				<farm-chip
					dense
					class="ml-1"
					:color="campaignMarginDetail.campaignCommercialProductStatus ? 'primary' : 'neutral'"
				>
					{{ campaignMarginDetail.campaignCommercialProductStatus ? 'Ativa' : 'Inativa' }}
				</farm-chip>
			</div>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1"> Campanha: </farm-caption>
				<farm-caption tag="dd">{{ campaignMarginDetail.campaignName }}</farm-caption>
				<farm-chip
					dense
					class="ml-1"
					:color="campaignMarginDetail.campaignStatus ? 'primary' : 'neutral'"
				>
					{{ campaignMarginDetail.campaignStatus ? 'Ativa' : 'Inativa' }}
				</farm-chip>
			</div>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1"> Vigência: </farm-caption>
				<farm-caption tag="dd">
					{{ defaultDateFormat(campaignMarginDetail.vigCampaignCommercialProductStart) }}
					até
					{{ defaultDateFormat(campaignMarginDetail.vigCampaignCommercialProductEnd) }}
				</farm-caption>
			</div>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1">
					Taxa Basal:
				</farm-caption>
				<farm-caption tag="dd">
					{{ campaignMarginDetail.campaignCommercialProductTaxBase }}%
				</farm-caption>
			</div>
			<div class="d-flex align-center mb-4">
				<farm-caption tag="dt" variation="semiBold" class="mr-1">
					Última Atualização:
				</farm-caption>
				<farm-caption tag="dd">
					{{ defaultDateFormat(campaignMarginDetail.updatedAtCampaignCommercialProduct) }}
				</farm-caption>
			</div>
		</dl>

		<template v-if="operationsToShow.length">
			<farm-line class="mb-4" />

			<template v-for="operation in operationsToShow">
				<div
					class="d-flex align-center mb-4"
					:key="`name-${operation.campaignOperationId}`"
				>
					<farm-caption tag="dt" variation="semiBold" class="mr-1">
						Operação:
					</farm-caption>
					<farm-caption tag="dd">
						{{
							OPERATION_TYPES_ENUM[operation.operationType]
								? OPERATION_TYPES_ENUM[operation.operationType].name
								: 'Não encontrado'
						}}
					</farm-caption>
				</div>

				<DashedCard :key="operation.campaignOperationId" class="pa-4 mb-4">
					<div class="dashed-card__item">
						<farm-idcaption copy-text="">
							<template #title>
								<farm-caption variation="semiBold">
									Intervalo Vencimento
								</farm-caption>
							</template>
							<template #subtitle>
								<farm-caption>
									{{ defaultDateFormat(operation.dtWorkMarginStarted) }}
									até
									{{ defaultDateFormat(operation.dtWorkMarginEnd) }}
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
					<div class="dashed-card__item">
						<farm-idcaption copy-text="">
							<template #title>
								<farm-caption variation="semiBold">
									Período de Desembolso Início/Fim
								</farm-caption>
							</template>
							<template #subtitle>
								<farm-caption>
									{{
										defaultDateFormat(operation.startDisbursementDateOperation)
									}}
									até
									{{ defaultDateFormat(operation.endDisbursementDateOperation) }}
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
					<div class="dashed-card__item">
						<farm-idcaption copy-text="">
							<template #title>
								<farm-caption variation="semiBold"> Margem </farm-caption>
							</template>
							<template #subtitle>
								<farm-caption
									v-if="operation.marginTax >= 0 && operation.marginTax !== null"
								>
									{{ operation.marginTax }}%
								</farm-caption>
								<farm-tooltip v-else>
									Não foi encontrada margem comercial cadastrada
									<template #activator>
										<farm-icon size="16" color="warning" variation="darken">
											alert-circle-outline
										</farm-icon>
									</template>
								</farm-tooltip>
							</template>
						</farm-idcaption>
					</div>
					<div class="dashed-card__item">
						<farm-idcaption copy-text="">
							<template #title>
								<farm-caption variation="semiBold">
									Taxa Total (a.m.)
								</farm-caption>
							</template>
							<template #subtitle>
								<farm-caption v-if="operation.totalMarginBase">
									{{ operation.totalMarginBase }}%
								</farm-caption>
								<farm-tooltip v-else>
									Não foi encontrada margem comercial cadastrada
									<template #activator>
										<farm-icon size="16" color="warning" variation="darken">
											alert-circle-outline
										</farm-icon>
									</template>
								</farm-tooltip>
							</template>
						</farm-idcaption>
					</div>
					<div class="dashed-card__item">
						<farm-idcaption copy-text="">
							<template #title>
								<farm-caption variation="semiBold"> N+ </farm-caption>
							</template>
							<template #subtitle>
								<farm-caption>
									{{ isNaN(parseInt(operation.n)) ? '-' : operation.n }}
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
				</DashedCard>
			</template>
		</template>
	</div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import DashedCard from '@/components/DashedCard';

import { OPERATION_TYPES_ENUM } from '@/constants';

export default defineComponent({
	components: {
		DashedCard,
	},
	props: {
		campaignMarginDetail: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const operationsToShow = computed(
			() =>
				props.campaignMarginDetail?.marginOperations?.filter(
					({ campaignOperationId }) => !!campaignOperationId
				) || []
		);

		return {
			OPERATION_TYPES_ENUM,
			operationsToShow,
			defaultDateFormat,
		};
	},
});
</script>
