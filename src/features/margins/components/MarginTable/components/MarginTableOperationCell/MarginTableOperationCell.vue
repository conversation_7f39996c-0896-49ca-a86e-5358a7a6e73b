<template>
	<farm-bodytext type="2" variation="regular" v-if="operation">
		{{ operation.marginTax }}% a.m. ({{ operation.totalMarginBase }}%)
	</farm-bodytext>
	<farm-tooltip v-else-if="operation === null">
		Não foi encontrada margem comercial cadastrada
		<template #activator>
			<farm-icon size="md" color="warning" variation="darken">alert-circle-outline</farm-icon>
		</template>
	</farm-tooltip>
	<farm-bodytext type="2" variation="regular" v-else> - </farm-bodytext>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		operation: {
			type: [Object, Boolean],
			default: null,
		},
	},
	setup() {
		return {};
	},
});
</script>
