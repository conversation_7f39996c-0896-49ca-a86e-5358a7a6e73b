<template>
	<farm-card tag="article">
		<header class="pa-4">
			<farm-bodytext variation="bold">
				{{ item.productName }}

				<farm-tooltip v-if="hasAnyMissingMargin">
					Não foi encontrada margem comercial cadastrada

					<template #activator>
						<farm-icon color="warning" variation="darken" size="md">
							alert-circle-outline
						</farm-icon>
					</template>
				</farm-tooltip>
			</farm-bodytext>
		</header>

		<MarginTable :items="item.campaigns" :product-id="item.productId" />
	</farm-card>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';

import MarginTable from '../MarginTable';

export default defineComponent({
	components: {
		MarginTable,
	},
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const hasAnyMissingMargin = computed(() =>
			props.item.campaigns.some(
				campaign =>
					campaign.marginOperations?.some(
						operation => operation.marginTax === null && !!operation.campaignOperationId
					) || false
			)
		);

		return {
			hasAnyMissingMargin,
		};
	},
});
</script>
