import type { Campaign } from '@/features/campaigns/types';
import type { RequestQueryString } from '@/types';

export type GetCampaignMarginListRequest = {
	query: RequestQueryString;
};
export type GetCommercialProductsListRequest = {
	query: RequestQueryString;
};


export type GetCampaignMarginDetailRequest = {
	params: {
		campaign_id: Campaign['id'];
	};
	query: {
		commercial_product_id: Campaign['commercialProducts'][number]['id'];
		product_id: number;
	};
};

export type GetCampaignMarginHistoryListRequest = {
	params: {
		campaign_id: Campaign['id'];
	};
	query: {
		page: string | number;
		limit: string | number;
		commercial_product_id: Campaign['commercialProducts'][number]['id'];
		product_id: number;
	};
};

export type ImportCampaignMarginListRequest = {
	payload: FormData;
};
