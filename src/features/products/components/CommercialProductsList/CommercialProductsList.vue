<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" v-for="(item, index) in data" :key="index">
				<commercial-products-card :data="item" :settlementTypeList="settlementTypeList" :modalityList="modalityList" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente associar um Produto Comercial." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import CommercialProductsCard from '../CommercialProductsCard';

export default defineComponent({
	name:"commercial-products-list",
	props: {
		data: {
			type: Array,
			default: () => [],
		},
		settlementTypeList: {
			type: Array,
			default: () => [],
		},
		modalityList: {
			type: Array,
			default: () => [],
		},
	},
	components:{
		CommercialProductsCard
	},
	setup(props){
		const { data, settlementTypeList, modalityList } = toRefs(props);

		const isDataEmpty = computed(()=>{
			return Array.isArray(data.value) && data.value.length === 0;
		});

		return {
			isDataEmpty,
			settlementTypeList,
			modalityList
		};
	}
});
</script>
