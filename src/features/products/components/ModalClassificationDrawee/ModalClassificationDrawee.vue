<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header title="Sacados na Classificação" @onClose="handleCloseModal" />
		</template>
		<template v-slot:content>
			<div class="no-padding">
				<farm-box>
					<v-data-table
						v-if="!isError"
						hide-default-footer
						class="elevation-0 pb-4 mt-0 table-cadastros-clients-list"
						:headers="headersModal"
						:items="dataTable"
						:server-items-length="dataTable.length"
						:hide-default-header="showCustomHeader()"
						:options.sync="options"
						:header-props="headerProps"
					>
						<template slot="no-data">
							<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
						</template>

						<template v-slot:[`item.status`]="{ item }">
							<StatusActiveAndInactive :status="item.status" />
						</template>

						<template v-slot:header="{ props: { headers } }" v-if="showCustomHeader()">
							<farm-datatable-header
								firstSelected
								:headers="headers"
								:sortClick="sortClicked"
								:selectedIndex="8"
								@onClickSort="onSort"
							/>
						</template>

						<template v-slot:footer>
							<farm-datatable-paginator
								class="mt-6"
								:page="currentPage"
								:totalPages="totalPages"
								@onChangePage="onChangePage"
								@onChangeLimitPerPage="onChangeLimitPerPage"
							/>
						</template>
					</v-data-table>
					<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
						<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
					</div>
					<farm-loader mode="overlay" v-if="isLoading" />
				</farm-box>
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Fechar"
				:hasCancel="false"
				@onConfirm="handleCloseModal"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import { headersModal } from '../../configurations/headers';

export default defineComponent({
	components: {
		StatusActiveAndInactive,
	},
	mixins: [pageable],
	props: {
		value: {
			required: true,
			type: Boolean,
		},
		data: {
			type: Array,
			default: () => [],
		},
		idClassification: {
			type: [Number, String],
		},
	},
	data() {
		return {
			headersModal,
			dataTable: [],
			totalPages: 0,
			options: {},
			headerProps: {
				sortByText: 'Ordenar por',
			},
			sortClicked: [],
			lastSearchFilters: {},
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
		};
	},
	computed: {
		...mapGetters('cadastros', {
			classificationDraweeData: 'classificationDraweeData',
			classificationDraweeDataRequestStatus: 'classificationDraweeDataRequestStatus',
		}),
		isLoading(): boolean {
			return this.classificationDraweeDataRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.classificationDraweeDataRequestStatus.type === RequestStatusEnum.ERROR;
		},
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getProductClassificationDrawee: 'getProductClassificationDrawee',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getProductClassificationDrawee({
				filters: this.filters,
				idProduct: this.currentId,
				idClassification: this.idClassification,
			});
		},
		reload(): void {
			this.doSearch();
		},
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			this.filters = {
				...this.filters,
				...this.hasSort,
			};
			this.getProductClassificationDrawee({
				filters: this.filters,
				idProduct: this.currentId,
				idClassification: this.idClassification,
			});
		},
		handleCloseModal(): void {
			this.$emit('onClose');
		},
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		updatedTable(): void {
			this.dataTable = this.classificationDraweeData.content;
		},
		updatedTotalPages(): void {
			this.totalPages = this.classificationDraweeData.totalPages;
		},
	},
	watch: {
		classificationDraweeDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedTable();
				this.updatedTotalPages();
			}
		},
	},
});
</script>

<style scoped>
.no-padding {
	width: calc(100% + 32px);
	margin: 0 -16px;
}
</style>
