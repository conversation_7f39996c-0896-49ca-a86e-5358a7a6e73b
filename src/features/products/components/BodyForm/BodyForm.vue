<template>
	<farm-container>
		<HeaderForm v-if="isEdit" :data="dataHeaderForm" />
		<TabsForm
			:tabList="tabs"
			:valueDefault="DATA"
			:isEdit="isEdit"
			@onUpdateCurrentTab="onUpdateCurrentTab"
		/>
		<TabData
			v-if="isTabData"
			:isEdit="isEdit"
			@onSubmit="onSubmit"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabWallet
			v-if="isTabWallet"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabClassification
			v-if="isTabClassification"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabFinancialVehicle
			v-if="isTabFinancialVehicle"
			:data="dataHeaderForm"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabDrawee
			v-if="isTabDrawee"
			@onUpdateHeaderForm="onUpdateHeaderForm"
			@onUpdateFooterFormData="onUpdateFooterFormData"
		/>
		<TabOriginatorsAssignors
			v-if="isTabOriginatorsAssignors"
			@onUpdateHeaderForm="onUpdateHeaderForm"
			@onUpdateFooterFormData="onUpdateFooterFormData"
		/>
		<TabComemercialProducts
			v-if="isTabComemercialProducts"
			@onUpdateHeaderForm="onUpdateHeaderForm"
			@onUpdateFooterFormData="onUpdateFooterFormData" />

		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:hiddenButton="isHiddenButton"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { BodyFormDataType } from '@/types';
import TabsForm, { TabsFormTypes } from '@/components/TabsForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import {
	EDIT,
	DATA,
	FINANCIAL_VEHICLE,
	DRAWEE,
	ORIGINATORS_ASSIGNORS,
	COMMERCIAL_PRODUCTS,
} from '@/constants';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import TabData from '../TabData';
import TabWallet from '../TabWallet';
import TabClassification from '../TabClassification';
import TabFinancialVehicle from '../TabFinancialVehicle';
import TabDrawee from '../TabDrawee';
import TabOriginatorsAssignors from '../TabOriginatorsAssignors';
import TabComemercialProducts from '../TabComemercialProducts';
import { headerPage } from '../../configurations/headersPages';
import { tabDefault, tabEdit } from '../../configurations/tabs';
import { WALLET, CLASSIFICATION } from '../../constants';

type ProductsBodyFormDataType = BodyFormDataType & {
	DATA: string;
	WALLET: string;
	FINANCIAL_VEHICLE: string;
	CLASSIFICATION: string;
	DRAWEE: string;
	ORIGINATORS_ASSIGNORS: string;
	dataFooterForm: FooterFormDataType;
	dataHeaderForm: HeaderFormTypes;
};

export default defineComponent({
	components: {
		TabsForm,
		FooterForm,
		HeaderForm,
		TabData,
		TabWallet,
		TabClassification,
		TabFinancialVehicle,
		TabDrawee,
		TabOriginatorsAssignors,
		TabComemercialProducts,
	},
	props: {
		type: {
			type: String,
		},
	},
	data(): ProductsBodyFormDataType {
		return {
			disabledButtonFooter: null,
			tabs: tabDefault,
			currentTab: DATA,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			DATA,
			WALLET,
			CLASSIFICATION,
			FINANCIAL_VEHICLE,
			labelButton: 'Cadastrar',
			dispatchSubmit: null,
			DRAWEE,
			ORIGINATORS_ASSIGNORS,
		};
	},
	computed: {
		isEdit(): boolean {
			return this.type === EDIT;
		},
		isTabData(): boolean {
			return this.currentTab === DATA;
		},
		isTabWallet(): boolean {
			return this.currentTab === WALLET;
		},
		isTabClassification(): boolean {
			return this.currentTab === CLASSIFICATION;
		},
		isTabFinancialVehicle(): boolean {
			return this.currentTab === FINANCIAL_VEHICLE;
		},
		isHiddenButton(): boolean {
			return this.currentTab !== DATA;
		},
		isTabDrawee(): boolean {
			return this.currentTab === DRAWEE;
		},
		isTabComemercialProducts(): boolean {
			return this.currentTab === COMMERCIAL_PRODUCTS;
		},
		isTabOriginatorsAssignors(): boolean {
			return this.currentTab === ORIGINATORS_ASSIGNORS;
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	methods: {
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			this.tabs = tabEdit;
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/produtos`,
			});
		},
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onUpdateCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
		},
		onUpdateHeaderForm(data: HeaderFormTypes): void {
			this.dataHeaderForm = parseDataHeader(data, headerPage);
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
	},
});
</script>
