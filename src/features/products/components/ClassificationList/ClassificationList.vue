<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<ClassificationCard
					:data="item"
					@onClickEdit="onClickEdit"
					@onClickRemove="onClickRemove"
					@openModalDrawee="openModalDrawee"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente adicionar uma classificação." />
			</farm-box>
		</farm-row>
		<ModalClassificationDrawee
			v-if="showModalClassificationDrawee"
			v-model="showModalClassificationDrawee"
			:idClassification="currentIdClassification"
			@onClose="onCloseModal"
		/>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ClassificationCard from '../ClassificationCard';
import ModalClassificationDrawee from '../ModalClassificationDrawee';

export default defineComponent({
	components: {
		ClassificationCard,
		ModalClassificationDrawee,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			showModalClassificationDrawee: false,
			currentIdClassification: 0,
		};
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		onClickEdit(item): void {
			this.$emit('handleClickItemEdit', item);
		},
		onClickRemove(item): void {
			this.$emit('handleClickItemRemove', item);
		},
		openModalDrawee(item) {
			this.currentIdClassification = item.id;
			this.showModalClassificationDrawee = true;
		},
		onCloseModal() {
			this.showModalClassificationDrawee = false;
		},
	},
});
</script>
