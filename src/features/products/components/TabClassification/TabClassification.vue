<template>
	<farm-box>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					v-if="canWrite"
					class="v-btn--responsive mt-8 mb-8"
					customIcon="plus"
					title="Adicionar Classificação"
					:to="`/admin/cadastros/produtos/${currentId}/classificacao/novo`"
					:icon="true"
				>
					Adicionar Classificação
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<ClassificationList
			v-if="!isError"
			:data="dataList"
			@handleClickItemEdit="handleClickItemEdit"
			@handleClickItemRemove="handleClickItemRemove"
		/>
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	pageable,
	notificationWrapper,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import { format } from '@/helpers/formatUpdateUser';

import ClassificationList from '../ClassificationList';
import { walletAndClassificationSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		ClassificationList,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'createdAt_DESC',
			sortOptions: sort,
			dataList: [],
			lastSearchFilters: { page: 0, limit: 10 },
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			filterInputKey: 'search',
			totalPages: 1,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			classificationData: 'classificationData',
			classificationDataRequestStatus: 'classificationDataRequestStatus',
			deleteClassificationRequestStatus: 'deleteClassificationRequestStatus',
		}),
		isLoading(): boolean {
			return (
				this.classificationDataRequestStatus === RequestStatusEnum.START ||
				this.deleteClassificationRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return this.classificationDataRequestStatus.type === RequestStatusEnum.ERROR;
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getProductClassification: 'getProductClassification',
			deleteProductClassification: 'deleteProductClassification',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getProductClassification({
				filters: { ...this.filters, ...this.hasSort },
				idProduct: this.currentId,
			});
		},
		reload(): void {
			this.doSearch();
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.getProductClassification({
				filters: { ...this.filters, ...this.hasSort },
				idProduct: this.currentId,
			});
		},
		handleClickItemEdit(data): void {
			const idClassification = data.id;
			const idProduct = this.currentId;
			const path = `/admin/cadastros/produtos/${idProduct}/editar/classificacao/${idClassification}`;
			this.$router.push({
				path,
			});
		},
		async handleClickItemRemove(data) {
			const idClassification = data.id;
			const idProduct = this.currentId;
			this.$dialog
				.confirm(
					{
						body: `Deseja realmente excluir a classificação <b>${stripTags(
							data.name
						)}?</b>`,
						title: 'Excluir Classificação',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.deleteProductClassification({
						idProduct,
						idClassification,
					});
				})
				.catch(() => {});
		},
		updatedList(): void {
			this.dataList = this.classificationData.content;
			this.totalPages = this.classificationData.totalPages;
		},
		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.classificationData.header.name,
				listIcons: [this.classificationData.header.id, this.classificationData.header.type],
			});
		},
		updatedDateFooter(): void {
			const updatedFooterFormData = format(this.classificationData.meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},
		refreshPage(): void {
			setTimeout(() => {
				this.doSearch();
			}, 1500);
		},
	},
	watch: {
		classificationDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedHeader();
				this.updatedDateFooter();
			}
		},
		deleteClassificationRequestStatus(newValue: string): void {
			notificationWrapper(newValue, 'Classificação excluída', 'excluír a Classificação');
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.refreshPage();
			}
		},
	},
});
</script>
