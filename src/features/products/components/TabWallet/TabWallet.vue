<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Carteira"
					:hasExtraFilters="false"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					v-if="canWrite"
					class="v-btn--responsive mt-8 mb-8"
					customIcon="plus"
					title="Adicionar Carteira"
					:to="`/admin/cadastros/produtos/${currentId}/carteira/novo`"
					:icon="true"
				>
					Adicionar <PERSON>
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<WalletList
			v-if="!isError"
			:data="dataList"
			@handleClickItemEdit="handleClickItemEdit"
			@handleClickItemRemove="handleClickItemRemove"
		/>
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	pageable,
	notificationWrapper,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import { format } from '@/helpers/formatUpdateUser';

import WalletList from '../WalletList';
import { walletAndClassificationSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		WalletList,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'createdAt_DESC',
			sortOptions: sort,
			dataList: [],
			lastSearchFilters: { page: 0, limit: 10 },
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			filterInputKey: 'search',
			totalPages: 1,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			walletData: 'walletData',
			walletDataRequestStatus: 'walletDataRequestStatus',
			deleteWalletRequestStatus: 'deleteWalletRequestStatus',
		}),
		isLoading(): boolean {
			return (
				this.walletDataRequestStatus === RequestStatusEnum.START ||
				this.deleteWalletRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return this.walletDataRequestStatus.type === RequestStatusEnum.ERROR;
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getProductWallet: 'getProductWallet',
			deleteProductWallet: 'deleteProductWallet',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getProductWallet({
				filters: { ...this.filters, ...this.hasSort },
				id: this.currentId,
			});
		},
		reload(): void {
			this.doSearch();
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.getProductWallet({
				filters: { ...this.filters, ...this.hasSort },
				id: this.currentId,
			});
		},
		updatedList(): void {
			this.dataList = this.walletData.content;
			this.totalPages = this.walletData.totalPages;
		},
		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.walletData.header.name,
				listIcons: [this.walletData.header.id, this.walletData.header.type],
			});
		},
		updatedDateFooter(): void {
			const updatedFooterFormData = format(this.walletData.meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},
		handleClickItemEdit(data): void {
			const idWallet = data.id;
			const idProduct = this.currentId;
			const path = `/admin/cadastros/produtos/${idProduct}/editar/carteira/${idWallet}`;
			this.$router.push({
				path,
			});
		},
		handleClickItemRemove(data) {
			this.$dialog
				.confirm(
					{
						body: `Deseja realmente excluir a carteira <b>${stripTags(data.name)}?</b>`,
						title: 'Excluir Carteira',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.deleteProductWallet({
						idProduct: this.currentId,
						idWallet: data.id,
					});
				})
				.catch(() => {});
		},
		refreshPage(): void {
			setTimeout(() => {
				this.doSearch();
			}, 1500);
		},
	},
	watch: {
		walletDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedHeader();
				this.updatedDateFooter();
			}
		},
		deleteWalletRequestStatus(newValue: string): void {
			notificationWrapper(newValue, 'Carteira excluída', 'excluír a Carteira');
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.refreshPage();
			}
		},
	},
});
</script>
