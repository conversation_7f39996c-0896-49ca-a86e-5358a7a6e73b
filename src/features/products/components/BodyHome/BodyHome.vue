<template>
	<farm-container>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Produto"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="right">
				<farm-btn-confirm
					v-if="canWrite"
					class="farm-btn--responsive mt-8"
					customIcon="plus"
					title="Adicionar Produto"
					to="/admin/cadastros/produtos/novo"
					:icon="true"
				>
					Adicionar Produto
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<Filters @onApply="searchListener" v-show="filter" key="filters" />
		</collapse-transition>
		<HomeTable
			v-if="!isError"
			:data="dataTable"
			:filterCurrent="filters"
			:paginationTotalPages="paginationTotalPages"
			:paginationPageActive="currentPage"
			@onRequest="onRequest"
		/>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import Filters from '../Filters/';
import HomeTable from '../HomeTable';

export default defineComponent({
	components: {
		Filters,
		HomeTable,
	},
	mixins: [pageable],
	data() {
		return {
			lastSearchFilters: {},
			filter: false,
			filterInputKey: 'name',
			filters: {
				page: 0,
				limit: 10,
			},
			dataTable: [],
			RequestStatusEnum,
			paginationTotalPages: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			productsData: 'productsData',
			productsDataRequestStatus: 'productsDataRequestStatus',
		}),
		isLoading(): boolean {
			return this.productsDataRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.productsDataRequestStatus.type === RequestStatusEnum.ERROR;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getProducts: 'getProducts',
		}),
		reload(): void {
			this.doSearch();
		},
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getProducts({
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		onRequest(filtersActive): void {
			this.lastSearchFilters = { ...filtersActive };
			const payload = {
				filters: filtersActive,
			};
			this.getProducts(payload);
		},
		updatedTable(): void {
			this.dataTable = this.productsData.content;
			this.paginationTotalPages = this.productsData.totalPages;
		},
	},
	watch: {
		productsDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedTable();
			}
		},
	},
});
</script>
