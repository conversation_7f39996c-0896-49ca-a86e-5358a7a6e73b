<template>
	<farm-form v-model="valid">
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label for="form-product-classification-name" required> Nome </farm-label>
				<farm-textfield-v2
					uppercase
					v-model="form.name"
					id="form-product-classification-name"
					:rules="[rules.required, errorForm]"
					@change="changeInput"
				/>
			</farm-col>
			<farm-col cols="12" md="2" v-if="isEdit">
				<farm-idcaption
					class="margem-top-idCaption"
					icon="account-multiple-outline"
					copyText=""
					:link="true"
					@onLinkClick="onOpenModal"
				>
					<template v-slot:title> Sacados </template>
					<template v-slot:subtitle>{{ data.drawees || 0 }}</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12" md="2" v-if="isEdit">
				<farm-idcaption class="margem-top-idCaption" icon="calendar" copyText="">
					<template v-slot:title> Data de Criação </template>
					<template v-slot:subtitle>
						{{ data.createdAt ? defaultDateFormat(data.createdAt) : 'N/A' }}
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12" md="3" v-if="isEdit">
				<farm-idcaption class="margem-top-idCaption" icon="update" copyText="">
					<template v-slot:title> Última Atualização </template>
					<template v-slot:subtitle>
						{{ data.updatedAt ? defaultDateFormat(data.updatedAt) : 'N/A' }}
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<ModalClassificationDrawee
			v-if="showModalClassificationDrawee"
			v-model="showModalClassificationDrawee"
			:idClassification="data.idClassification"
			@onClose="onCloseModal"
		/>
	</farm-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import ModalClassificationDrawee from '../ModalClassificationDrawee';

export default defineComponent({
	components: {
		ModalClassificationDrawee,
	},
	props: {
		form: {
			type: Object,
		},
		isEdit: {
			type: Boolean,
		},
		data: {
			type: Object,
		},
		errorForm: {
			type: [Boolean, String],
		},
	},

	data() {
		return {
			valid: true,
			defaultDateFormat,
			showModalClassificationDrawee: false,
			dataName: '',
			rules: {
				required: value => !!value || 'Campo obrigatório',
			},
		};
	},
	methods: {
		onCloseModal() {
			this.showModalClassificationDrawee = false;
		},
		onOpenModal() {
			this.showModalClassificationDrawee = true;
		},
		changeInput() {
			if (this.errorForm !== true) {
				this.$emit('onUpdatedStatusForm', true);
			}
		},
	},
	watch: {
		valid(newValue: boolean): void {
			this.$emit('onDisabledButtonFooter', newValue);
		},
	},
});
</script>
<style>
.margem-top-idCaption {
	margin-top: 21px;
}
</style>
