<template>
	<farm-container>
		<HeaderForm :data="dataHeaderForm" v-if="!isError" withLine />
		<TitlePageForm
			v-if="!isError"
			value="Veículo Financeiro"
			noPipe
			:label="!isEdit ? 'Adicionar' : 'Editar'"
		/>
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4">
				<farm-label for="form-select-financialVehicle" :required="true">
					Veículo Financeiro
				</farm-label>
				<farm-select-auto-complete
					id="form-select-financialVehicle"
					v-model="form.financialVehicleId"
					item-text="label"
					item-value="value"
					:items="financialVehicle"
					:rules="[rules.required]"
					:disabled="isEdit"
					@change="onChangeFinancialVehicle"
				></farm-select-auto-complete>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError" y-grid-gutters>
			<farm-col cols="12" md="8" v-if="showFields">
				<ListInformation :data="dataListInformation" class="mb-4" />
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-concentrationPercentage">
					Percentual de Concentração
					<farm-tooltip>
						Percentual de Concentração aprovado por exceção.
						<template v-slot:activator>
							<farm-icon size="sm" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</farm-label>
				<farm-textfield-v2
					id="form-concentrationPercentage"
					maxlength="4"
					v-model="form.concentrationPercentage"
					:rules="[rules.percentage]"
				/>
			</farm-col>
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Cessão </farm-label>
				<farm-switcher class="mt-4" id="" v-model="form.assignment" block></farm-switcher>
			</farm-col>
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Emissão de Lastro </farm-label>
				<farm-switcher
					class="mt-4"
					id=""
					v-model="form.emissionOfBallast"
					block
				></farm-switcher>
			</farm-col>
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Originação </farm-label>
				<farm-switcher class="mt-4" id="" v-model="form.origination" block></farm-switcher>
			</farm-col>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT } from '@/constants';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { parsePayload, parseConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import ListInformation from '@/components/ListInformation';
import TitlePageForm from '@/components/TitlePageForm';

import { listBodyFinancialVehicleAssociation, headerPage } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		HeaderForm,
		FooterForm,
		ListInformation,
		TitlePageForm,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: null,
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataListInformation: listBodyFinancialVehicleAssociation,
			labelButton: 'Associar',
			form: {
				financialVehicleId: '',
				assignment: false,
				origination: false,
				emissionOfBallast: false,
				concentrationPercentage: '',
			},
			financialVehicle: [],
			rules: {
				required: value => !!value || 'Campo obrigatório',
				percentage: value => {
					if (value.length === 0) {
						return true;
					}
					const REGEXP = /^(\d+,)*(\d+)$/;
					if (!REGEXP.test(value)) {
						return 'Valor inválido';
					}
					if (parseInt(value, 10) < 0 || parseInt(value, 10) > 100) {
						return 'Campo inválido';
					}
					return true;
				},
			},
			financialVehicleInfo: [],
			showFields: false,
			idFinancialVehicleSelected: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
			productsAssignorFinancialVehicleNeverData: 'productsAssignorFinancialVehicleNeverData',
			productsAssignorFinancialVehicleNeverRequestStatus:
				'productsAssignorFinancialVehicleNeverRequestStatus',
			saveProductAssociationFinancialVehicleRequestStatus:
				'saveProductAssociationFinancialVehicleRequestStatus',
			productsAssignorFinancialVehicleIdData: 'productsAssignorFinancialVehicleIdData',
			productsAssignorFinancialVehicleIdRequestStatus:
				'productsAssignorFinancialVehicleIdRequestStatus',
		}),
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentId(): number {
			return this.$route.params.id;
		},
		currentIdFinancialVehicle(): number {
			return this.$route.params.idFinancialVehicle;
		},
		isLoading(): boolean {
			return (
				this.productsAssignorFinancialVehicleNeverRequestStatus ===
					RequestStatusEnum.START ||
				this.productHeaderRequestStatus === RequestStatusEnum.START ||
				this.saveProductAssociationFinancialVehicleRequestStatus ===
					RequestStatusEnum.START ||
				this.productsAssignorFinancialVehicleIdRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return (
				this.productsAssignorFinancialVehicleNeverRequestStatus.type ===
					RequestStatusEnum.ERROR ||
				this.productsAssignorFinancialVehicleIdRequestStatus.type ===
					RequestStatusEnum.ERROR
			);
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.labelButton = 'Salvar';
			this.showFields = true;
		}
		this.load();
	},
	methods: {
		...mapActions('cadastros', {
			getProductNotAssociationFinancialVehicle: 'getProductNotAssociationFinancialVehicle',
			getProductHeader: 'getProductHeader',
			saveProductAssociationFinancialVehicle: 'saveProductAssociationFinancialVehicle',
			getByIdProductAssociationFinancialVehicle: 'getByIdProductAssociationFinancialVehicle',
		}),
		load(): void {
			const payload = {
				idProduct: this.currentId,
			};
			this.getProductHeader(payload);
			if (this.isEdit) {
				this.getByIdProductAssociationFinancialVehicle({
					idProduct: this.currentId,
					idFinancialVehicle: this.currentIdFinancialVehicle,
				});
				return;
			}
			this.getProductNotAssociationFinancialVehicle(payload);
		},
		reload(): void {
			this.load();
		},
		onCancel(): void {
			const idProduct = this.currentId;
			const path = `/admin/cadastros/produtos/${idProduct}/editar?path=veiculos_financeiro`;
			this.$router.push({
				path,
			});
		},
		onSave(): void {
			const payload = this.createPayload();
			this.saveProductAssociationFinancialVehicle({
				idProduct: this.currentId,
				idFinancialVehicle: this.currentIdFinancialVehicle,
				type: this.isEdit ? 'edit' : 'new',
				payload,
			});
		},
		onChangeFinancialVehicle(value): void {
			if (this.isEdit) {
				return;
			}
			if (value !== '') {
				this.showFields = true;
				this.disabledButtonFooter = true;
				this.idFinancialVehicleSelected = value;
				this.getDataFinancialVehicle(value);
			} else {
				this.showFields = false;
				this.disabledButtonFooter = false;
			}
		},
		getDataFinancialVehicle(id: string): void {
			const data = this.productsAssignorFinancialVehicleNeverData.content.filter(item => {
				return item.id === parseInt(id, 10);
			});
			this.updatedDataListInformation([data[0].id, data[0].type, data[0].document || 'N/A']);
		},
		updatedDataListInformation(data): void {
			const newValues = listBodyFinancialVehicleAssociation.map((item, index) => {
				return {
					...item,
					value: data[index],
				};
			});
			this.dataListInformation = newValues;
		},
		updatedHeader(data): void {
			const header = {
				title: data.name,
				listIcons: [data.id, data.type],
			};
			this.dataHeaderForm = parseDataHeader(header, headerPage);
		},
		updatedSelectFinancialVehicle(data): void {
			this.financialVehicle = [...data];
		},
		createPayload() {
			const financialVehicleId = parseInt(this.form.financialVehicleId, 10);
			const concentrationPercentage = parsePayload(this.form.concentrationPercentage);
			const payload = {
				...this.form,
				concentrationPercentage,
			};
			if (this.isEdit) {
				return payload;
			}
			return {
				...payload,
				financialVehicleId,
			};
		},
		updatedForm(data): void {
			const concentrationPercentage = parseConcentrationPercentage(
				data.concentrationPercentage,
				'',
				''
			);
			this.form = {
				...this.form,
				assignment: data.assignment,
				origination: data.origination,
				emissionOfBallast: data.emissionOfBallast,
				commercialProductId: data.id.toString(),
				concentrationPercentage,
			};
			this.updatedSelectFinancialVehicle([
				{
					label: data.name,
					value: data.id.toString(),
				},
			]);
			this.updatedDataListInformation([data.id, data.type, data.document || 'N/A']);
		},
	},
	watch: {
		productsAssignorFinancialVehicleNeverRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedSelectFinancialVehicle(
					this.productsAssignorFinancialVehicleNeverData.select
				);
			}
		},
		productHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.productHeaderData);
			}
		},
		'form.concentrationPercentage': function (newValue): void {
			const REGEXP = /^(\d+,)*(\d+)$/;
			if (newValue.length > 0 && !REGEXP.test(newValue)) {
				this.disabledButtonFooter = false;
				return;
			}
			this.disabledButtonFooter = true;
		},

		saveProductAssociationFinancialVehicleRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				let mensage = '';
				if (this.isEdit) {
					mensage = ` Associação ao veículo financeiro atualizada com sucesso!`;
				} else {
					const productSeleted =
						this.productsAssignorFinancialVehicleNeverData.select.filter(item => {
							return item.value === this.idFinancialVehicleSelected;
						});
					mensage = `Produto associado com sucesso ao veículo financeiro <b>${productSeleted[0].label}</b>.`;
				}
				notification(RequestStatusEnum.SUCCESS, mensage);
				setTimeout(() => {
					this.onCancel();
				}, 2000);
			}

			if (newValue.type === RequestStatusEnum.ERROR) {
				let financialVehicleName = '';
				if (this.isEdit) {
					financialVehicleName =
						this.productsAssignorFinancialVehicleNeverData.content.name;
				} else {
					const data = this.productsAssignorFinancialVehicleNeverData.select.filter(
						item => {
							return item.value === this.currentIdProduct;
						}
					);
					financialVehicleName = data[0].label;
				}
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível associar o veículo financeiro <b>${financialVehicleName}</b> ao produto.`
				);
			}
		},
		productsAssignorFinancialVehicleIdRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedForm(this.productsAssignorFinancialVehicleIdData.content);
			}
		},
	},
});
</script>
