<template>
	<farm-container>
		<HeaderForm :data="dataHeaderForm" />
		<TabsForm :tabList="tabs" :valueDefault="IMPORT" @onUpdateCurrentTab="onUpdateCurrentTab" />
		<TabImport
			v-if="isTabImport"
			@onSubmit="onSubmit"
			@changeRouter="changeRouter"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabHistoric v-if="isTabHistoric" @onUpdateHeaderForm="onUpdateHeaderForm" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="false"
			:hiddenButton="isTabHistoric"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import TabsForm, { TabsFormTypes } from '@/components/TabsForm';

import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { HISTORIC, IMPORT } from '@/constants';
import { tabsImport } from '@/configurations/tabs';

import TabImport from '../TabImport';
import TabHistoric from '../TabHistoric';
import { headerPage } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		HeaderForm,
		FooterForm,
		TabsForm,
		TabImport,
		TabHistoric,
	},
	data() {
		return {
			IMPORT,
			HISTORIC,
			disabledButtonFooter: null,
			labelButton: 'Importar',
			dispatchSubmit: null,
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			tabs: tabsImport,
			currentTab: IMPORT,
		};
	},
	computed: {
		isTabHistoric(): boolean {
			return this.currentTab === HISTORIC;
		},
		isTabImport(): boolean {
			return this.currentTab === IMPORT;
		},
		isLoading(): boolean {
			return false;
		},
		isError(): boolean {
			return false;
		},
		currentProductId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},

	methods: {
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onCancel(): void {
			const idProduct = this.currentProductId;
			this.$router.push({
				path: `/admin/cadastros/produtos/${idProduct}/editar?path=sacados`,
			});
		},
		onUpdateCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
		},
		onUpdateHeaderForm(data: HeaderFormTypes): void {
			this.dataHeaderForm = parseDataHeader(data, headerPage);
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		changeRouter(): void {
			this.currentTab = HISTORIC;
			this.$router.replace({ query: { path: 'historico' } });
			this.tabs = [...tabsImport];
		},
	},
	mounted(): void {
		this.tabs = [...tabsImport];
	},
});
</script>
