<template>
	<farm-box>
		<farm-form class="mb-6" v-model="valid">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-status"> Status </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="filters.status"
						:items="statusList"
						item-text="label"
						item-value="id"
					></farm-select-auto-complete>
				</farm-col>
				<farm-col cols="12" md="9" class="mt-2">
					<farm-btn-confirm
						outlined
						class="button-top"
						title="Aplicar Filtros"
						@click="apply"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="ml-0 ml-sm-2 button-top"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { RegistersStatusValueEnum } from '@/constants';

export default defineComponent({
	data() {
		return {
			valid: false,
			filters: {
				status: null,
			},
		};
	},
	computed: {
		statusList() {
			return Object.keys(RegistersStatusValueEnum)
				.filter(el => isNaN(Number(el)))
				.map(key => ({
					id: RegistersStatusValueEnum[key],
					label: key,
				}))
				.sort((x, y) => x.label.localeCompare(y.label));
		},
	},
	methods: {
		apply(): void {
			this.$emit('onApply', { ...this.filters });
		},
		onFilterClear(): void {
			this.filters = {
				status: null,
			};
			this.$emit('onApply', { ...this.filters });
		},
	},
});
</script>

<style scoped>
.button-top {
	margin-top: 25px;
}
</style>
