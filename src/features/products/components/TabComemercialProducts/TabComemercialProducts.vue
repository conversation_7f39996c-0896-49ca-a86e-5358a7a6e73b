<template>
	<farm-box>
		<commercial-products-filter-and-button-new
			v-if="!isError"
			@onSortSelect="onSortSelect"
			@onFilter="onFilter"
			@onFilterClicked="onFilterClicked"
			@onClickMainFilter="onClickMainFilter"
			@onInputChangeMainFilter="onInputChangeMainFilter"
			@onRedirectToAssociate="onRedirectToAssociate"
		/>
		<commercial-products-list
			v-if="!isError"
			class="mt-4"
			:settlementTypeList="commercialProductSettlementType"
			:modalityList="commercialProductModality"
			:data="commercialProduct"
		/>
		<farm-row extra-decrease class="mb-4" v-if="!isError && commercialProduct.length > 0">
			<farm-box>
				<farm-datatable-paginator
					class="mt-6 mb-n6"
					:page="page"
					:totalPages="commercialProductByIdPagination.totalPages || 1"
					:initialLimitPerPage="commercialProductByIdPagination.limit"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch, computed } from 'vue';
import { useRouter } from '@/composibles';
import { useCommercialProductById } from '@/features/products/composables/useCommercialProductById';
import { usePageable } from '@/composibles/usePageable';
import { format } from '@/helpers/formatUpdateUser';
import CommercialProductsFilterAndButtonNew from '../CommercialProductsFilterAndButtonNew';
import CommercialProductsList from '../CommercialProductsList';
import { useProductHeader } from '../../composables/useProductHeader';
import { useProductById } from '../../composables/useProductById';
import { useCommercialProductSettlementType } from '../../composables/useCommercialProductSettlementType';
import { useCommercialProductModality } from '../../composables/useCommercialProductModality';

export default defineComponent({
	name: 'tab-commercial-products',
	components: {
		CommercialProductsFilterAndButtonNew,
		CommercialProductsList,
	},
	setup(_, { emit }) {

		const filterCurrent = ref({
			page: 0,
			limit: 10,
			order: 'DESC',
			orderby: 'associationStart',
		});

		const isError = ref(false);

		const {
			commercialProduct,
			commercialProductByIdPagination,
			isLoadingCommercialProductById,
			getCommercialProductById } = useCommercialProductById();

		const { commercialProductSettlementType, isLoadingCommercialProductSettlementType, getCommercialProductSettlementType} = useCommercialProductSettlementType();
		const { commercialProductModality, isLoadingCommercialProductModality, getCommercialProductModality } = useCommercialProductModality();

		const { productByIdMeta, getProductById, productByIdIsSuccess } = useProductById();

		const { productHeaderData, getProductHeader, isSuccessProductHeader } = useProductHeader();

		const router = useRouter();

		const {
			page,
			isOpenFilter,
			isFilterCounter,
			pagination,
			onClickMainFilter,
			onInputChangeMainFilter,
			onFiltersApplied,
			onChangePage,
			onChangePageLimit,
			onSortSelect
		} = usePageable(
			{
				calbackFn: params => {
					filterCurrent.value = { ...params };
					const dataParams = {
						id: router.currentRoute.params.id,
						filters:{
							...filterCurrent.value
						}
					};
					getCommercialProductById(dataParams, updatedPage);
				},
				filters: {
					page: 0,
					limit: 10,
				},
				keyInputSearch: 'search',
				sort: {
					order: 'DESC',
					orderby: 'associationStart',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			commercialProductByIdPagination.value
		);

		const isLoading = computed(() => {
			return isLoadingCommercialProductById.value || isLoadingCommercialProductSettlementType.value || isLoadingCommercialProductModality.value;
		});

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id,
				filters: {
					page:0,
					limit:10,
					order: 'DESC',
					orderby: 'associationStart',
				}
			};
			getCommercialProductById(payload);
			getProductHeader(router.currentRoute.params.id);
			getProductById(router.currentRoute.params.id);
			getCommercialProductSettlementType();
			getCommercialProductModality();
		}


		function onReload(): void {
			load();
		}

			function updatedPage(hasError): void{
			if(hasError){
				isError.value = true;
				return;
			}
			isError.value = false;
		}

		function onFilter(data): void{
			filterCurrent.value ={
				...filterCurrent.value,
				...data
			};
			const payload = {
				id: router.currentRoute.params.id,
				filters: {
					...filterCurrent.value,
				}
			};
			getCommercialProductById(payload, updatedPage);
		}

		function onFilterClicked(value): void{
			onFiltersApplied(value);
			if(value === false){
				filterCurrent.value = {
					page: 0,
					limit: filterCurrent.value.limit,
					order: 'DESC',
					orderby: 'associationStart',
				};
				const payload = {
					id: router.currentRoute.params.id,
					filters: {
						...filterCurrent.value,
					}
				};
				getCommercialProductById(payload, updatedPage);
			}
		}

		function updatedHeader(): void {
		const newValue = productHeaderData.value = { title: productHeaderData.value.name,
			listIcons: [productHeaderData.value.id, productHeaderData.value.type]};
			emit('onUpdateHeaderForm', newValue);
		}

		function updateFooter(): void {
			emit('onUpdateFooterFormData', format(productByIdMeta.value));
		}

		function onRedirectToAssociate(): void {
			router.push({path: `/admin/cadastros/produtos/${router.currentRoute.params.id}/associacao_productos_comerciais/novo`});
		}

		watch(isSuccessProductHeader, (newValue) => {
			if(newValue){
				updatedHeader();
			}
		});

		watch(productByIdIsSuccess, (newValue) => {
			if(newValue){
				updateFooter();
			}
		});

		onMounted(() => {
			load();
		});

		return {
			commercialProduct,
			isError,
			isLoading,
			page,
			isOpenFilter,
			isFilterCounter,
			pagination,
			onClickMainFilter,
			onFiltersApplied,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			commercialProductByIdPagination,
			commercialProductSettlementType,
			commercialProductModality,
			onInputChangeMainFilter,
			onReload,
			onFilter,
			onFilterClicked,
			onRedirectToAssociate
		};
	},
});
</script>
