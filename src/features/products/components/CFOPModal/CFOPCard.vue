<template>
	<div :class="['farm-card', { 'farm-card--active': internalSelected }]">
		<Cards>
			<template slot="header">
				<farm-row class="d-flex space-between align-center">
					<farm-col cols="1" align="start" class="card-action">
						<farm-checkbox
							class="ml-3"
							v-model="internalSelected"
							size="md"
							ref="checkbox"
							:value="true"
						/>
					</farm-col>
					<farm-col cols="11">
						<CfopCardTitleAndValueList :data="cardHeaderList" />
					</farm-col>
				</farm-row>
			</template>
			<template slot="body">
				<farm-row y-grid-gutters>
					<farm-col cols="12">
						<CardTextBody label="Descrição" :value="data.description" />
					</farm-col>
				</farm-row>
			</template>
		</Cards>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import CfopCardTitleAndValueList from './CfopCardTitleAndValueList.vue';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
		CfopCardTitleAndValueList,
	},
	props: {
		data: {
			type: Object as () => any,
			required: true,
		},
		selected: {
			type: Boolean,
			required: false,
		},
	},
	setup(props, { emit }) {
		const internalSelected = ref(props.selected);
		const cardHeaderList = ref([
			{
				title: 'Código',
				value: props.data.code,
			},
			{
				title: 'Nome',
				value: props.data.name || 'Venda de mercadoria adquirida ou recebida de terceiros',
			},
			{
				title: 'Origem/Destino',
				value: props.data.sameState ? 'Mesmo Estado' : 'Outro Estado',
			},
			{
				title: 'Amostra/Bonificação',
				value: props.data.sample ? 'Sim' : 'Não',
			},
			{
				title: 'Venda Performada?',
				value: props.data.salesPerformed ? 'Sim' : 'Não',
			},
		]);

		watch(
			() => props.selected,
			newValue => {
				internalSelected.value = newValue;
			}
		);

		watch(internalSelected, newValue => {
			if (newValue === null) {
				emit('checkbox-changed', { id: props.data.id, selected: false });
			} else {
				emit('checkbox-changed', { id: props.data.id, selected: newValue });
			}
		});

		watch(
			() => props.selected,
			newValue => {
				internalSelected.value = newValue;
			},
			{ immediate: true }
		);

		return {
			internalSelected,
			cardHeaderList,
			formatValueOrNA,
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
			formatConcentrationPercentage,
		};
	},
});
</script>

<style>
.farm-card--active {
	border: solid 1px var(--farm-primary-base);
	border-radius: 7px;
}
</style>
