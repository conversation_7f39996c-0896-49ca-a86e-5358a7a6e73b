<template>
	<farm-modal v-model="isModalOpen" :offsetBottom="68" size="default" :persistent="true">
		<template v-slot:content>
			<farm-row justify="space-between" class="mt-6 ml-2">
				<farm-idcaption icon="clipboard-text-outline" copyText="">
					<template v-slot:title>
						<farm-heading type="6"> Alterar CFOPs Permitidos </farm-heading>
					</template>
					<template v-slot:subtitle>
						Selecione os CFOPs permitidos entre o Parceiro e o Veículo Financeiro.
					</template>
				</farm-idcaption>
			</farm-row>

			<farm-line class="mt-6 mb-6" />

			<farm-col col="12" class="mb-1">
				<div class="product-info mb-4">
					<img src="@/assets/icons/bank.svg" alt="Imagem referente a dinheiro" />
					<farm-row>
						<farm-col cols="12">
							<farm-bodysmall
								variation="bold"
								:style="{ fontSize: ' 12px !important' }"
							>
								{{ vehicleData.financialVehicle }}
							</farm-bodysmall>
						</farm-col>
						<farm-col cols="12" class="detail-product">
							<farm-bodysmall class="supplier-info" variation="bold">
								ID:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ vehicleData.financialVehicleId }} |
							</farm-bodysmall>
							<farm-bodysmall class="supplier-info" variation="bold">
								CNPJ:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ vehicleDocument }} |
							</farm-bodysmall>
							<farm-bodysmall class="supplier-info" variation="bold">
								Tipo:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ vehicleType }}
							</farm-bodysmall>
						</farm-col>
					</farm-row>
				</div>

				<farm-row justify="space-between" class="filter">
					<farm-col md="8" sm="12" lg="8">
						<farm-form-mainfilter
							:hasExtraFilters="false"
							label="Buscar Fornecedor"
							@onInputChange="onInputChangeMainFilter"
						>
							<div>
								<farm-label>
									Buscar CFOP
									<farm-tooltip>
										Realize sua busca pelo código do CFOP
										<template v-slot:activator>
											<farm-icon size="sm" color="gray"
												>help-circle</farm-icon
											>
										</template>
									</farm-tooltip>
								</farm-label>
							</div>
						</farm-form-mainfilter>
					</farm-col>
					<farm-col md="4" align="end" class="mt-3" :style="{ paddingTop: '15px' }">
						<farm-select
							v-model="filterCurrent.defaultFilter"
							item-text="text"
							item-value="value"
							:items="sortByOptions"
							@change="onSortSelect"
						/>
					</farm-col>
				</farm-row>
			</farm-col>

			<farm-row
				justify="space-between"
				align="center"
				class="mb-6"
				v-if="cfopsList.length > 0"
			>
				<farm-col md="6">
					<farm-chip color="primary" variation="lighten" :dense="true">
						{{ selectedCFPOPs.length }}
						selecionado(s)
					</farm-chip>
				</farm-col>
				<farm-col md="6" align="end" v-if="cfopsList.length > 0">
					<div class="footer-form-buttons">
						<farm-btn
							class="farm-btn--responsive"
							title="Selecionar todos"
							outlined
							@click="selectAllProviders"
						>
							Selecionar todos
						</farm-btn>
						<farm-btn
							class="farm-btn--responsive ml-2"
							title="Desmarcar Selecionados"
							outlined
							@click="deselectAllProviders"
						>
							Desmarcar Selecionados
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>

			<div>
				<farm-loader v-if="isLoading" mode="overlay" />
				<farm-row v-if="cfopsList.length === 0 && !isLoading && !errorMessage">
					<farm-box>
						<farm-emptywrapper :bordered="false" />
					</farm-box>
				</farm-row>
				<farm-row v-if="errorMessage && !isLoading">
					<farm-box>
						<farm-emptywrapper
							class="mb-8"
							title="Sem CFOPs habilitados no Veiculo Financeiro associado"
							subtitle="<span style='color: #858585'>Para alterar a parametrização dos CFOPs deste veiculo financeiro</span> <span ref='btnRedirect' style='cursor: pointer;
							color: var(--farm-primary-base);font-weight: bold;'>clique aqui</span>"
							:bordered="false"
							icon="alert-outline"
						/>
					</farm-box>
				</farm-row>
				<farm-row v-else>
					<farm-col cols="12" md="12" v-if="!isLoading" class="px-4">
						<CfopCard
							v-for="item in cfopsList"
							:data="item"
							:key="item.id"
							class="mb-6"
							:selected="selectedCFPOPs.includes(item.id)"
							@checkbox-changed="onCheckboxChanged"
						/>
					</farm-col>
				</farm-row>
			</div>

			<farm-datatable-paginator
				v-if="cfopsList.length > 0"
				:page="page"
				class="px-2"
				:has-gutter="false"
				:totalPages="cfopData.totalPages || 1"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				@onConfirm="sendCfopData"
				@onClose="closeModal"
				close-label="Cancelar"
				confirmLabel="Salvar"
				:isConfirmDisabled="cfopsList.length === 0 || errorMessage"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { onMounted, ref, watch } from 'vue';
import { updateCfops, getVehicleDocument } from '../../services/index';
import CfopCard from './CFOPCard.vue';
import { usePageable } from '@/composibles/usePageable';
import { useCfops } from '@/composibles/useCFOP';

import { useRouter } from '@/composibles/useRouter';

export default {
	name: 'CFOPMODAL',
	components: {
		CfopCard,
	},
	props: {
		vehicleData: {
			type: Object,
			required: false,
		},
		isModalOpen: {
			type: Boolean,
			default: false,
		},
		closeModal: {
			type: Function,
			required: false,
		},
		CommercialproductIdSelected: {
			type: Object,
			required: false,
		},
		productId: {
			type: Number,
			required: false,
		},
	},
	setup(props, { emit }) {
		const filterCurrent = ref({
			page: 0,
			limit: 10,
			order: 'ASC',
			orderby: 'code',
			defaultFilter: 'code_ASC',
		});

		const vehicleDocument = ref(null);
		const vehicleType = ref(null);
		const sortByOptions = ref([
			{ text: 'Código Crescente - Código Decrescente', value: 'code_ASC' },
			{ text: 'Código Decrescente - Código Crescente ', value: 'code_DESC' },
		]);
		const authorizedProvidersCount = ref(10);
		const isLoading = ref(false);
		const isSelectedAll = ref(true);
		const btnRedirect = ref(null);

		const router = useRouter();

		const { cfopsList, cfopData, selectedCFPOPs, fetchCfops, getCfopsSelected, errorMessage } =
			useCfops({
				productId: props.productId,
				vehicleData: props.vehicleData,
				commercialProductId: props.CommercialproductIdSelected,
			});

		const fetchVehicleDocument = async () => {
			try {
				const response = await getVehicleDocument(props.vehicleData.financialVehicleId);
				vehicleDocument.value = response.data.content.document;
				vehicleType.value = response.data.content.type;
			} catch (error) {
				console.error('Erro ao buscar documentos do veículo:', error);
			}
		};

		const sendCfopData = async () => {
			try {
				const payload = {
					cfops: selectedCFPOPs.value,
				};

				const data = {
					productId: props.productId,
					commercialProductId: props.CommercialproductIdSelected,
					financialVehicleId: props.vehicleData?.financialVehicleId,
					payload,
				};

				await updateCfops(data);

				emit('showSuccesModal');
			} catch (error) {
				console.error('Erro ao enviar CFOPs:', error);
			}
		};

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
		} = usePageable(
			{
				calbackFn: fetchCfops,
				filters: filterCurrent.value,
				sort: { order: 'ASC', orderby: 'code' },
				keyInputSearch: 'search',
				charInputSearch: 1,
			},
			{ pageNumber: 0, pageSize: 10, totalElements: 0, totalPages: 1 }
		);

		const onCheckboxChanged = ({ id, selected }) => {
			if (selected) {
				if (!selectedCFPOPs.value.includes(id)) {
					selectedCFPOPs.value.push(id);
				}
			} else {
				selectedCFPOPs.value = selectedCFPOPs.value.filter(item => item !== id);
			}
		};

		const selectAllProviders = () => {
			cfopsList.value.forEach(cfop => {
				cfop.selected = true;
				if (!selectedCFPOPs.value.includes(cfop.id)) {
					selectedCFPOPs.value.push(cfop.id);
				}
			});
		};

		const deselectAllProviders = () => {
			cfopsList.value.forEach(cfop => {
				cfop.selected = false;
			});
			selectedCFPOPs.value = [];
		};

		const loadData = async () => {
			isLoading.value = true;
			await getCfopsSelected();
			await fetchCfops(filterCurrent.value);
			await fetchVehicleDocument();
			isLoading.value = false;
		};

		onMounted(async () => {
			if (props.isModalOpen) {
				isLoading.value = true;
				await loadData();
				isLoading.value = false;
			}
			btnRedirect.value = document.querySelector('span[ref="btnRedirect"]');
			if (btnRedirect.value) {
				btnRedirect.value.addEventListener('click', function () {
					router.push(
						`/admin/cadastros/veiculos_financeiros/${props.vehicleData.financialVehicleId}/editar?path=cfop`
					);
				});
			}
		});

		watch(
			() => pagination,
			newPagination => {
				filterCurrent.value.page = newPagination.pageNumber;
				filterCurrent.value.limit = newPagination.pageSize;
				loadData();
			}
		);

		return {
			cfopData,
			vehicleDocument,
			filterCurrent,
			selectedCFPOPs,
			sortByOptions,
			authorizedProvidersCount,
			cfopsList,
			onChangePageLimit,
			isLoading,
			onInputChangeMainFilter,
			onChangePage,
			page,
			onSortSelect,
			selectAllProviders,
			deselectAllProviders,
			sendCfopData,
			isSelectedAll,
			onCheckboxChanged,
			vehicleType,
			errorMessage,
			btnRedirect,
		};
	},
};
</script>

<style scoped>
.product-info {
	margin-bottom: 1rem;
	display: flex;
	gap: 10px;
	margin: 0px -6px;
}
.filter {
	margin: 0px -20px;
}
.detail-product {
	display: flex;
	gap: 0.15rem;
}
.loading-indicator {
	text-align: center;
	margin-top: 20px;
	font-size: 16px;
	color: #666;
}
.supplier-info {
	font-size: 12px !important;
	color: rgba(92, 92, 92, 1);
}
</style>
