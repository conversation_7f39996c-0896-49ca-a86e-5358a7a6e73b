<template>
	<farm-box>
		<farm-form class="d-flex align-center" v-model="valid">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-status"> Tipo do Veículo Financeiro </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="filters.type"
						:items="listType"
						item-text="label"
						item-value="value"
					></farm-select-auto-complete>
				</farm-col>
				<farm-row class="pl-4 pt-2">
					<farm-col>
						<farm-btn-confirm
							outlined
							class="button-top"
							title="Aplicar Filtros"
							@click="apply"
						>
							Aplicar Filtros
						</farm-btn-confirm>
						<farm-btn
							plain
							class="ml-0 ml-sm-2 button-top"
							title="Limpar Filtros"
							@click="onFilterClear"
						>
							Limpar Filtros
						</farm-btn>
					</farm-col>
				</farm-row>
			</farm-row>
		</farm-form>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		listType: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			valid: false,
			filters: { type: null },
		};
	},
	methods: {
		apply(): void {
			this.$emit('onApply', { ...this.filters });
		},
		onFilterClear(): void {
			this.filters = { type: null };
			this.$emit('onApply', { ...this.filters });
		},
	},
});
</script>

<style scoped>
.button-top {
	margin-top: 25px;
}
</style>
