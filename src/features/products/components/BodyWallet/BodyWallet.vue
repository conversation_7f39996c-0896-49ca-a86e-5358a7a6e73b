<template>
	<farm-container>
		<HeaderForm v-if="!isError" withLine :data="dataHeaderForm" />
		<TitlePageForm
			v-if="!isError"
			value="Carteira"
			noPipe
			:label="!isEdit ? 'Adicionar' : 'Editar'"
		/>
		<WalletForm
			v-if="!isError"
			:form="form"
			:isEdit="isEdit"
			:infoWallet="infoWallet"
			:errorForm="errorForm"
			@onUpdatedStatusForm="onUpdatedStatusForm"
			@onDisabledButtonFooter="onDisabledButtonFooter"
		/>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="false"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notificationWrapper } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT } from '@/constants';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import TitlePageForm from '@/components/TitlePageForm';

import WalletForm from '../WalletForm';
import { headerPage } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		HeaderForm,
		FooterForm,
		WalletForm,
		TitlePageForm,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: true,
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			labelButton: 'Cadastrar',
			form: {
				name: '',
			},
			infoWallet: {},
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			errorForm: true,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			walletData: 'walletData',
			walletDataRequestStatus: 'walletDataRequestStatus',
			walletDataById: 'walletDataById',
			walletDataByIdRequestStatus: 'walletDataByIdRequestStatus',
			saveWalletRequestStatus: 'saveWalletRequestStatus',
		}),
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentId(): number {
			return this.$route.params.id;
		},
		currentIdWallet(): number {
			return this.$route.params.idWallet;
		},
		isLoading(): boolean {
			return (
				this.walletDataRequestStatus === RequestStatusEnum.START ||
				this.walletDataByIdRequestStatus === RequestStatusEnum.START ||
				this.saveWalletRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return (
				this.walletDataRequestStatus.type === RequestStatusEnum.ERROR ||
				this.walletDataByIdRequestStatus.type === RequestStatusEnum.ERROR
			);
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.loadEditWallet();
			return;
		}
		this.loadNewWallet();
	},
	methods: {
		...mapActions('cadastros', {
			getProductWallet: 'getProductWallet',
			getProductWalletById: 'getProductWalletById',
			saveProductWallet: 'saveProductWallet',
		}),
		loadNewWallet(): void {
			this.getProductWallet({
				filters: { ...this.filters, ...this.hasSort },
				id: this.currentId,
			});
		},
		loadEditWallet(): void {
			this.labelButton = 'Salvar';
			this.getProductWalletById({
				idProduct: this.currentId,
				idWallet: this.currentIdWallet,
			});
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/produtos/${this.currentId}/editar?path=carteira`,
			});
		},
		createPayload() {
			return {
				name: this.form.name.toUpperCase(),
			};
		},
		onSave(): void {
			const payload = this.createPayload();
			this.saveProductWallet({
				type: this.isEdit ? 'edit' : 'new',
				payload,
				idProduct: this.currentId,
				idWallet: this.currentIdWallet,
			});
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		updatedHeader(data): void {
			this.dataHeaderForm = parseDataHeader(data, headerPage);
		},
		updatedForm(data): void {
			this.form = {
				name: data.name,
			};
		},
		updatedInfoWallet(data): void {
			this.infoWallet = {
				id: this.currentIdWallet,
				draweeCount: data.drawees,
				createdAt: data.createdAt,
				updatedAt: data.updatedAt,
			};
		},
		createMessage(type): string {
			const actionSucess = this.isEdit ? 'atualizada' : 'cadastrada';
			const actionError = this.isEdit ? 'atualizada' : 'cadastrada';
			const message = {
				ERROR: `${actionError} a Carteira`,
				SUCCESS: `Carteira ${actionSucess}`,
			};
			return message[type];
		},
		redirectToHome(): void {
			setTimeout(() => {
				this.onCancel();
			}, 1500);
		},
		onUpdatedStatusForm(data): void {
			this.errorForm = data;
		},
	},
	watch: {
		walletDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.walletData.header;
				this.updatedHeader({
					title: this.walletData.header.name,
					listIcons: [this.walletData.header.id, this.walletData.header.type],
				});
			}
		},
		walletDataByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader({
					title: this.walletDataById.content.productName,
					listIcons: [this.walletDataById.content.id, this.walletDataById.content.type],
				});
				this.updatedForm(this.walletDataById.content);
				this.updatedInfoWallet(this.walletDataById.content);
			}
		},
		saveWalletRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notificationWrapper(
					newValue,
					this.createMessage(RequestStatusEnum.SUCCESS),
					this.createMessage(RequestStatusEnum.ERROR)
				);
				this.redirectToHome();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				this.onUpdatedStatusForm('Nome de carteira existente.');
			}
		},
	},
});
</script>
