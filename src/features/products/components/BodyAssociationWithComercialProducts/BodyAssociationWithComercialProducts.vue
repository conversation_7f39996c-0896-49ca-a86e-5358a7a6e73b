<template>
	<farm-container>
		<informations-header @onUpdatedFooter="() => {}" withLine />
		<title-page-form :value="`${isEdit ? 'Editar' : 'Associar'} Produto Comercial`" noPipe />
		<farm-form v-model="valid" ref="form">
			<farm-row align="center">
				<farm-col cols="6" md="4">
					<farm-label for="form-address-type" required> Produto Comercial </farm-label>
					<farm-select-auto-complete
						id="form-address-type"
						item-text="name"
						item-value="id"
						:disabled="isEdit"
						v-model="productSelected"
						:items="options"
						:rules="[rules.required]"
						@change="onChageSelect"
					/>
				</farm-col>
				<farm-col v-if="isShowFields" cols="6" md="4">
					<list-information :data="dataList" />
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="12">
					<association-details
						v-if="isShowFields"
						:data="dataAssociation"
						@onChangeFinancialVehicle="onChangeFinancialVehicle"
						@onChangeModalities="onChangeModalities"
						@onChangeValidForm="onChangeValidForm"
						@goToCommercialProduct="goToCommercialProduct"
						:CommercialproductIdSelected="productSelected"
					/>
				</farm-col>
				<div v-if="dataAssociationIsError" class="mt-4 mb-8 d-flex justify-center">
					<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
				</div>
			</farm-row>

			<farm-loader mode="overlay" v-if="isLoading" />
			<footer-form
				:labelButton="isEdit ? 'Salvar' : 'Associar'"
				:isDisabledButton="isEnableButton && isNotEmpty"
				:showLayoutData="false"
				@onCancel="onCancel"
				@onSave="onSave"
			/>
		</farm-form>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed, watch, toRefs } from 'vue';

import { useRouter } from '@/composibles';
import TitlePageForm from '@/components/TitlePageForm';
import ListInformation from '@/components/ListInformation';
import FooterForm from '@/components/FooterForm';
import { EDIT } from '@/constants';

import { useCommercialProductAvailable } from '../../composables/useCommercialProductAvailable';
import { useCommercialProductAssociationById } from '../../composables/useCommercialProductAssociationById';
import { useAddAssociationWithCommercialProduct } from '../../composables/useAddAssociationWithCommercialProduct';
import { useRedirectCommercialProduct } from '../../composables/useRedirectCommercialProduct';

import { listAssociation } from './configurations';

import InformationsHeader from '../InformationsHeader';
import AssociationDetails from './components/AssociationDetails';
import { useEditAssociationWithCommercialProduct } from '../../composables/useEditAssociationWithCommercialProduct';

export default defineComponent({
	name: 'body-association-with-comercial-products',
	components: {
		InformationsHeader,
		TitlePageForm,
		ListInformation,
		AssociationDetails,
		FooterForm,
	},
	props: {
		type: {
			type: String,
		},
	},
	setup(props) {
		const productSelected = ref(null);
		const valid = ref(false);
		const { type } = toRefs(props);
		const rules = ref({
			required: text => !!text || 'Campo obrigatório',
		});
		const dataList = ref(listAssociation);
		const isValidFormFinancialVehicle = ref(true);
		const isNotEmpty = ref(false);

		const payload = ref({
			modalities: [],
			financialVehicles: [],
		});

		const { data, isLoadingData, getCommercialProductAvailable } =
			useCommercialProductAvailable();

		const {
			dataAssociation,
			isShowFields,
			dataAssociationIsLoading,
			dataAssociationIsError,
			dataAssociationIsSuccess,
			getAssociationtById,
		} = useCommercialProductAssociationById();

		const { redirectCommercialProduct, redirectMenuCommercialProductById } =
			useRedirectCommercialProduct();

		const { createAssociationWithCommercialProduct, isLoadingAddAssociation } =
			useAddAssociationWithCommercialProduct();
		const { updateAssociationWithCommercialProduct, isLoadingEditAssociation } =
			useEditAssociationWithCommercialProduct();
		const router = useRouter();

		const isLoading = computed(
			() =>
				isLoadingData.value ||
				dataAssociationIsLoading.value ||
				isLoadingAddAssociation.value ||
				isLoadingEditAssociation.value
		);
		const isEnableButton = computed(
			() =>
				valid.value &&
				isValidFinancialVehicles.value &&
				isValidFormFinancialVehicle.value &&
				isValidModalities.value
		);

		const isValidFinancialVehicles = ref(false);
		const isValidModalities = ref(false);

		const isEdit = computed(() => type.value === EDIT);

		const options = computed(() => {
			if (isEdit.value) {
				return [
					{
						id: router.currentRoute.params.idCommercialProduct,
						name: router.currentRoute.params.name,
						status: 1,
					},
				];
			}
			return data.value;
		});

		function onChageSelect(value) {
			const params = {
				id: router.currentRoute.params.id,
				commercialProductId: value,
			};
			getAssociationtById(params);
		}

		function callRedirect(): void {
			const id = router.currentRoute.params.id;
			redirectCommercialProduct(id);
		}

		function transformObject(originalObject) {
			return {
				modalities: originalObject.modalities.map(item => {
					return {
						modalityId: item.modalityId,
						enabled: item.enabled ? 1 : 0,
						settlementTypes: item.settlementTypes.map(settlement => {
							return {
								settlementTypeId: settlement.settlementTypeId,
								enabled: settlement.enabled ? 1 : 0,
							};
						}),
					};
				}),
				financialVehicles: originalObject.financialVehicles.map(item => {
					return {
						financialVehicleId: item.financialVehicleId,
						enabled: item.enabled ? 1 : 0,
						percentage: parseFloat(String(item.percentage)?.replace(',', '.')),
					};
				}),
			};
		}

		function onSave() {
			const data = {
				id: router.currentRoute.params.id,
				commercialProductId: productSelected.value,

				payload: {
					...transformObject(payload.value),
				},
			};
			if (isEdit.value) {
				updateAssociationWithCommercialProduct(data, callRedirect);
				return;
			}
			createAssociationWithCommercialProduct(data, callRedirect);
		}

		function onCancel() {
			callRedirect();
		}

		function onChangeFinancialVehicle(newValue) {
			payload.value = {
				...payload.value,
				financialVehicles: newValue,
			};

			isValidFinancialVehicles.value = validateFinancialVehicles(newValue);
		}

		function onChangeModalities(newValue) {
			payload.value = {
				...payload.value,
				modalities: newValue,
			};

			isValidModalities.value = validateModalities(newValue);
		}

		function onReload() {
			const params = {
				id: router.currentRoute.params.id,
				commercialProductId: productSelected.value,
			};
			getAssociationtById(params);
		}

		function goToCommercialProduct() {
			const id = productSelected.value;
			redirectMenuCommercialProductById(id);
		}

		function onChangeValidForm(value) {
			isValidFormFinancialVehicle.value = value;
		}

		function validateFinancialVehicles(financialVehicles) {
			return financialVehicles.some(item => item.enabled);
		}

		function validateModalities(modalities) {
			return modalities.some(item => {
				return item.enabled && item.settlementTypes.some(settlement => settlement.enabled);
			});
		}

		watch(dataAssociationIsSuccess, newValue => {
			if (newValue) {
				payload.value = {
					modalities: dataAssociation.value.modality,
					financialVehicles: dataAssociation.value.financialVehicles,
				};
				isValidFinancialVehicles.value = validateFinancialVehicles(
					dataAssociation.value.financialVehicles
				);
				isValidModalities.value = validateModalities(dataAssociation.value.modality);
				const currentOptions = options.value.find(
					item => item.id === productSelected.value
				);
				isNotEmpty.value =
					dataAssociation.value.financialVehicles.length > 0 ||
					dataAssociation.value.modality.length > 0;
				const keys = ['id', 'status'];
				const newData = dataList.value.map((item, index) => {
					return {
						...item,
						value: currentOptions[keys[index]],
					};
				});

				newData.forEach(item => {
					if (item.subtitle === 'Status') {
						item.value = item.value === 1 ? 'Ativo' : 'Inativo';
					}
				});

				dataList.value = [...newData];
			}
		});

		function onLoad() {
			if (isEdit.value) {
				const params = {
					id: router.currentRoute.params.id,
					commercialProductId: router.currentRoute.params.idCommercialProduct,
				};
				getAssociationtById(params);
				productSelected.value = router.currentRoute.params.idCommercialProduct;
			} else {
				getCommercialProductAvailable(router.currentRoute.params.id);
			}
		}

		onMounted(() => {
			onLoad();
		});

		return {
			rules,
			productSelected,
			isShowFields,
			dataList,
			data,
			dataAssociation,
			isLoading,
			dataAssociationIsError,
			isEnableButton,
			valid,
			isEdit,
			options,
			isNotEmpty,
			onChageSelect,
			onReload,
			onSave,
			onCancel,
			onChangeFinancialVehicle,
			onChangeModalities,
			goToCommercialProduct,
			onChangeValidForm,
		};
	},
});
</script>
