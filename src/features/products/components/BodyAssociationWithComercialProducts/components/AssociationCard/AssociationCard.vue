<template>
	<farm-card>
		<farm-card-content>
			<div class="d-flex justify-space-between align-center">
				<div class="d-flex align-center gap">
					<div>
						<img
							src="@/assets/icons/currency_exchange.svg"
							alt="imagem referente a dinheiro"
						/>
					</div>
					<div>
						<farm-bodytext variation="bold" :type="1">
							Modalidades de {{item.modalityName}}
						</farm-bodytext>
						<farm-typography size="14px" colorVariation="darken" color="neutral">
							Escolha os tipos de operação de {{item.modalityName}} habilitados para essa associação.
						</farm-typography>
					</div>
				</div>
				<div class="d-flex flex-column align-start mb-3 mt-3">
					<div class="mr-5">
						<farm-label>
							Habilitar {{item.modalityName}}
						</farm-label>
					</div>
					<farm-switcher v-model="localEnabled" block />
				</div>
			</div>
			<farm-line />
			<div class="d-flex justify-start align-center">
				<div v-for="(settlementType, index) in item.settlementTypes" :key="settlementType.settlementTypeId" class="d-flex flex-column align-start mt-3">
					<div class="mr-5">
						<farm-label>
							{{settlementType.settlementTypeName}}
						</farm-label>
					</div>
					<farm-switcher v-model="localSettlementTypes[index].enabled" block :disabled="!localEnabled" />
				</div>
			</div>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">

import { defineComponent, ref, toRefs, watch } from 'vue';

export default defineComponent({
	name: 'association-card',
	props: {
		item: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			required: true
		}
	},
	setup(props, { emit }) {
		const { item, index } = toRefs(props);
		const localEnabled = ref(item.value.enabled);
		const localSettlementTypes = ref(item.value.settlementTypes);

		watch(localEnabled, (newValue) => {
			emit('toggleModalityEnabled', { modalityIndex: index.value, newValue});
			if(!newValue) {
				localSettlementTypes.value.forEach((settlementType) => {
					settlementType.enabled = false;
				});
				emit('toggleSettlementTypeEnabled', { modalityIndex: index.value, newValue: localSettlementTypes.value});
			}
		});

		watch(localSettlementTypes, (newValue) => {
			emit('toggleSettlementTypeEnabled', { modalityIndex: index.value, newValue});
		}, { deep: true });

		return {
			item,
			localEnabled,
			localSettlementTypes
		};
	}
});
</script>
<style lang="scss" scoped>
.gap {
	gap: 8px;
}
</style>

