<template>
	<farm-box class="mb-6">
		<div
			v-if="localFinancialVehicles.length && localModalities.length"
			class="d-flex justify-between align-center flex-column gap"
		>
			<association-card
				v-for="(modality, index) in localModalities"
				:item="modality"
				:key="index"
				:index="index"
				@toggleModalityEnabled="toggleModalityEnabled"
				@toggleSettlementTypeEnabled="toggleSettlementTypeEnabled"
				@onChangeFinancialVehiclePercentage="onChangeFinancialVehiclePercentage"
			/>
			<CFOPModal
				v-if="isCFOPModalOpen"
				:isModalOpen="isCFOPModalOpen"
				:vehicleData="selectedVehicle"
				:closeModal="handleCloseModal"
				:CommercialproductIdSelected="CommercialproductIdSelected"
				:productId="data.id"
				@showSuccesModal="handlePromptClose"
			/>
			<farm-modal v-model="showSuccessMessage" size="xs" :offsetTop="16" :offsetBottom="16">
				<template v-slot:content>
					<farm-row class="px-3">
						<farm-row justify="space-between">
							<farm-bodytext :type="2" variation="bold" class="px-3"
								>Sucesso</farm-bodytext
							>
							<farm-dialog-header class="pb-8" @onClose="handlePromptClose" />
						</farm-row>
					</farm-row>

					<farm-bodytext :type="2" variation="regular"
						>CFOPs alterados com sucesso!</farm-bodytext
					>
				</template>
			</farm-modal>
			<association-card-financial-vehicle
				v-if="localFinancialVehicles.length"
				:financialVehicles="localFinancialVehicles"
				@onChangeValidForm="onChangeValidForm"
				@onChangeEnable="onChangeFinancialVehicleEnable"
				@openCFOPModal="handleOpenCFOPModal"
				:urlType="urlPath"
				:warningModal="showWarning"
			/>
		</div>
		<farm-emptywrapper
			v-if="!localFinancialVehicles.length || !localModalities.length"
			title="Sem modalidades habilitadas e/ou veículos financeiros associados"
			subtitle="Para finalizar ou alterar a parametrização deste produto comercial <span ref='btnRedirect' style='cursor: pointer;
color: var(--farm-primary-base);font-weight: bold;'>clique aqui</span>"
			icon="alert-outline"
			:bordered="false"
		/>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, toRefs, watch, onBeforeUnmount } from 'vue';
import CFOPModal from '../../../CFOPModal/CFOPModal.vue';
import AssociationCard from '../AssociationCard';
import AssociationCardFinancialVehicle from '../AssociationCardFinancialVehicle';
import { useRouter } from '@/composibles';

export default defineComponent({
	name: 'association-details',
	components: {
		AssociationCard,
		AssociationCardFinancialVehicle,
		CFOPModal,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
		CommercialproductIdSelected: {
			type: Object,
			required: false,
		},
	},
	setup(props, { emit }) {
		const { data, CommercialproductIdSelected } = toRefs(props);
		const router = useRouter();
		const localModalities = ref(transformModality(data.value.modality));
		const btnRedirect = ref(null);
		const isCFOPModalOpen = ref(false);
		const selectedVehicle = ref(null);
		const showSuccessMessage = ref(false);

		const urlPath = ref('');
		const showWarning = ref(false);

		const handlePromptClose = () => {
			isCFOPModalOpen.value = false;
			showSuccessMessage.value = true;

			setTimeout(() => {
				showSuccessMessage.value = false;
			}, 1000);
		};

		function handleOpenCFOPModal(vehicle) {
			if (urlPath.value === 'Novo' || !vehicle.enabled) {
				showWarning.value = true;
				setTimeout(() => {
					showWarning.value = false;
				}, 3000);
			} else if (urlPath.value === 'Editar' || vehicle.enabled) {
				isCFOPModalOpen.value = true;
			}
			if (urlPath.value === 'Editar' || !vehicle.enabled) {
				showWarning.value = false;
			}
			selectedVehicle.value = { ...vehicle, vehicleType: data.value.type };
		}

		const localFinancialVehicles = ref(
			transformFinancialVehicles(data.value.financialVehicles)
		);

		function toggleModalityEnabled({ modalityIndex, newValue }) {
			localModalities.value[modalityIndex].enabled = newValue;
		}

		function toggleSettlementTypeEnabled({ modalityIndex, newValue }) {
			localModalities.value[modalityIndex].settlementTypes = newValue;
		}

		function transformModality(modalityArray) {
			return modalityArray.map(modality => ({
				...modality,
				enabled: modality.enabled === 1,
				settlementTypes: modality.settlementTypes.map(settlement => ({
					...settlement,
					enabled: settlement.enabled === 1,
				})),
			}));
		}

		function transformFinancialVehicles(financialVehiclesArray) {
			return financialVehiclesArray.map(financialVehicle => ({
				...financialVehicle,
				enabled: financialVehicle.enabled === 1,
			}));
		}

		function onChangeFinancialVehicleEnable({ index, value }) {
			localFinancialVehicles.value[index].enabled = value;
		}

		function onChangeFinancialVehiclePercentage({ index, value }) {
			localFinancialVehicles.value[index].percentage = value;
		}

		function onChangeValidForm(value) {
			emit('onChangeValidForm', value);
		}

		const handleCloseModal = () => {
			isCFOPModalOpen.value = false;
		};

		function checkUrlType(url) {
			let actionType = '';

			if (url.includes('/novo')) {
				actionType = 'Novo';
				urlPath.value = actionType;
			} else if (url.includes('/editar')) {
				actionType = 'Editar';
				urlPath.value = actionType;
			} else {
				actionType = 'Indefinido';
				urlPath.value = actionType;
			}
		}

		watch(
			localFinancialVehicles,
			newValue => {
				emit('onChangeFinancialVehicle', newValue);
			},
			{ deep: true }
		);

		watch(
			localModalities,
			newValue => {
				emit('onChangeModalities', newValue);
			},
			{ deep: true }
		);

		onMounted(() => {
			btnRedirect.value = document.querySelector('span[ref="btnRedirect"]');
			if (btnRedirect.value) {
				btnRedirect.value.addEventListener('click', function () {
					emit('goToCommercialProduct');
				});
			}
			checkUrlType(router.currentRoute.fullPath);
		});

		onBeforeUnmount(() => {
			if (btnRedirect.value) {
				btnRedirect.value.removeEventListener('click', function () {
					emit('goToCommercialProduct');
				});
			}
		});

		return {
			CommercialproductIdSelected,
			isCFOPModalOpen,
			showSuccessMessage,
			handleCloseModal,
			selectedVehicle,
			handleOpenCFOPModal,
			localModalities,
			localFinancialVehicles,
			data,
			toggleModalityEnabled,
			toggleSettlementTypeEnabled,
			onChangeFinancialVehicleEnable,
			onChangeFinancialVehiclePercentage,
			onChangeValidForm,
			handlePromptClose,
			urlPath,
			showWarning,
		};
	},
});
</script>
<style lang="scss" scoped>
.gap {
	gap: 24px;
}
</style>
