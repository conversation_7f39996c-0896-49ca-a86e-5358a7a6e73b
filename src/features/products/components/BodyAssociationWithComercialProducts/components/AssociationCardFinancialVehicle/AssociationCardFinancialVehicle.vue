<template>
	<farm-card>
		<farm-card-content>
			<div class="d-flex justify-space-between align-center">
				<div class="d-flex align-center gap">
					<div>
						<img src="@/assets/icons/bank.svg" alt="imagem referente a dinheiro" />
					</div>
					<div>
						<farm-bodytext variation="bold" :type="1">
							Veículos Financeiros
						</farm-bodytext>
						<farm-typography size="14px" colorVariation="darken" color="neutral">
							Escolha os Veículos Financeiros e seus percentuais de concentração. Após
							realizar a associação será possível alterar os CFOPs permitidos.
						</farm-typography>
					</div>
				</div>
			</div>
			<farm-line />
			<farm-col cols="12">
				<farm-alertbox v-if="warningModal" class="mt-3" color="neutral" icon="alert-circle"
					>Para realizar a configuração de CFOPs Permitidos é necessário concluir a
					associação.</farm-alertbox
				>
			</farm-col>
			<farm-row yGridGutters>
				<farm-col
					cols="6"
					v-for="(vehicle, index) in financialVehicles"
					:key="index"
					class="d-flex-item"
				>
					<farm-form v-model="valid" ref="form">
						<Cards
							:class="{
								'card-financial-vehicle': localFinancialVehicles[index].enabled,
							}"
						>
							<template slot="header">
								<farm-row>
									<farm-col cols="8">
										<farm-typography class="farm-bw-black" size="12px"
											>Veículo Financeiro</farm-typography
										>
										<CardTitleHeader :value="vehicle.financialVehicle" />
									</farm-col>

									<farm-col
										cols="4"
										class="d-flex"
										:style="{
											justifyContent: 'center',
											flexDirection: 'column',
										}"
									>
										<farm-label class="card-financial-vehicle-label">
											Usar Veículo Financeiro?
										</farm-label>
										<div class="card-financial-vehicle-label">
											<farm-switcher
												v-model="localFinancialVehicles[index].enabled"
												@input="value => onChangeEnable(value, index)"
												block
											/>
										</div>
									</farm-col>
								</farm-row>
							</template>
							<template slot="body">
								<farm-row>
									<farm-col cols="8">
										<farm-label for="inpuit-percentage">
											Percentual de Concentração
											<farm-tooltip>
												Percentual de concentração entre o Parceiro e o
												Veículos Financeiro.
												<template v-slot:activator>
													<farm-icon class="ml-1" size="sm" color="gray"
														>help-circle</farm-icon
													>
												</template>
											</farm-tooltip>
										</farm-label>
										<farm-textfield-v2
											id="inpuit-percentage"
											v-model="localFinancialVehicles[index].percentage"
											:disabled="!localFinancialVehicles[index].enabled"
											:rules="[rules.percentage]"
											@keyup="$event => onChangeInput($event, index)"
										/>
									</farm-col>
									<farm-col
										cols="4"
										class="d-flex"
										:style="{ justifyContent: 'center', alignItems: 'center' }"
									>
										<button
											type="button"
											@click="() => openCFOPModal(vehicle)"
											:style="{ display: 'flex', gap: '8px' }"
										>
											<farm-icon
												:color="
													urlType === 'Novo' ||
													!localFinancialVehicles[index].enabled
														? 'gray'
														: 'primary'
												"
												size="md"
												>clipboard-text-outline</farm-icon
											>
											<farm-typography
												tag="p"
												size="md"
												weight="700"
												:color="
													urlType === 'Novo' ||
													!localFinancialVehicles[index].enabled
														? 'gray'
														: 'primary'
												"
												>CFOPs Permitidos</farm-typography
											>
										</button>
									</farm-col>
								</farm-row>
							</template>
						</Cards>
					</farm-form>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, toRefs, watch } from 'vue';
import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';

interface FinancialVehicle {
	financialVehicle: string;
	enabled: boolean;
	percentage: number;
}

export default defineComponent({
	name: 'association-card-financial-vehicle',
	components: {
		Cards,
		CardTitleHeader,
		CardListTextHeader,
		CardTextBody,
	},
	props: {
		financialVehicles: {
			type: Array,
			required: true,
		},
		urlType: {
			type: String,
			required: false,
		},
		warningModal: {
			type: Boolean,
			required: false,
		},
	},
	setup(props, { emit }) {
		const { financialVehicles, warningModal } = toRefs(props);

		const localFinancialVehicles = ref<FinancialVehicle[]>(
			financialVehicles.value as FinancialVehicle[]
		);
		const localEnabled = ref(false);
		const valid = ref(false);

		const rules = ref({
			percentage: text => isValidPercentage(text),
		});

		function onChangeEnable(value: boolean, index: number) {
			emit('onChangeEnable', { value, index });
		}

		function isValidPercentage(value) {
			if (value.length === 0) {
				return 'Campo obrigatório';
			}
			const REGEXP = /^(\d+,)*(\d+)$/;
			if (!REGEXP.test(value)) {
				return 'Valor inválido';
			}
			if (parseInt(value, 10) < 0 || parseInt(value, 10) > 100) {
				return 'Campo inválido';
			}
			return true;
		}

		function onChangeInput(event: Event, index: number) {
			const target = event.target as HTMLInputElement;
			const value: string = target.value;
			emit('onChangeFinancialVehiclePercentage', { value, index });
		}

		function openCFOPModal(vehicle) {
			emit('openCFOPModal', vehicle);
		}

		watch(
			valid,
			newValue => {
				emit('onChangeValidForm', newValue);
			},
			{ deep: true }
		);

		onMounted(() => {
			if (
				Array.isArray(localFinancialVehicles.value) &&
				localFinancialVehicles.value.length > 0
			) {
				valid.value = localFinancialVehicles.value.some(vehicle => vehicle.percentage >= 0);
			}
		});

		return {
			rules,
			openCFOPModal,
			valid,
			localEnabled,
			financialVehicles,
			localFinancialVehicles,
			onChangeEnable,
			onChangeInput,
			warningModal,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './AssociationCardFinancialVehicle.scss';
</style>
