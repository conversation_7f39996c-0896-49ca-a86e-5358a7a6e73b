<template>
	<farm-box class="mb-6">
		<farm-row v-if="!isErrorProductHeader">
			<farm-col cols="12">
				<farm-heading :type="6" class="mb-4">
					{{ title || 'Carregando...' }}
				</farm-heading>
			</farm-col>
			<farm-col cols="12">
				<list-information
					class="mb-4"
					:data="dataList"
					:messageSucess="''"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="!isErrorProductHeader && withLine">
			<farm-line noSpacing />
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isErrorProductHeader" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';

import ListInformation from '@/components/ListInformation';
import { useRouter } from '@/composibles';

import { useProductHeader } from '../../composables/useProductHeader';

export default defineComponent({
	name: 'informations-header-comercial-products',
	components: {
		ListInformation
	},
	props:{
		withLine: {
			type:Boolean,
			default:false
		},
	},
	setup(){
		const router = useRouter();
		const {
			productHeaderData,
			getProductHeader,
			isLoadingProductHeader,
			isSuccessProductHeader,
			isErrorProductHeader

		} = useProductHeader();

		const title = ref('');
		const dataList = ref([
			{
				id: '',
				subtitle: 'ID',
				icon: 'clipboard-text-outline',
				value: '',
				copyText: '',
			},
			{
				id: '',
				subtitle: 'Tipo',
				icon: 'tag-outline',
				value: '',
				copyText: '',
			},

		]);
		const isError = ref(false);

		const isLoading = computed(() => {
			return (
				isLoadingProductHeader.value
			);
		});

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = router.currentRoute.params.id;

			getProductHeader(payload);
		}

		watch(isSuccessProductHeader, (value) => {
			if(value){
				title.value = productHeaderData.value.name;
				const keys = ['id', 'type'];
				const newData = dataList.value.map((item, index) => {
					return {
						...item,
						value: productHeaderData.value[keys[index]]
					};
				});
				dataList.value = [...newData];
			}
		});

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			title,
			dataList,
			productHeaderData,
			isErrorProductHeader,
			onReload
		};
	}
});
</script>
