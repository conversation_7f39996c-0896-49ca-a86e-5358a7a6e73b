<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Originador/Cedente"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<OriginatorsAssignorsFilters v-show="filter" key="filters" @onApply="searchListener" />
		</collapse-transition>
		<OriginatorsAssignorsList v-if="!isError" :data="dataList" />
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import OriginatorsAssignorsFilters from '../OriginatorsAssignorsFilters';
import OriginatorsAssignorsList from '../OriginatorsAssignorsList';

import { originatorsAssignorsSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		OriginatorsAssignorsFilters,
		OriginatorsAssignorsList,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			filterInputKey: 'search',
			dataFilter: [],
			dataList: [],
			totalPages: 1,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
			productsAssociatedOriginatorsAssignorsData:
				'productsAssociatedOriginatorsAssignorsData',
			productsAssociatedOriginatorsAssignorsRequestStatus:
				'productsAssociatedOriginatorsAssignorsRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.productHeaderRequestStatus,
				this.productsAssociatedOriginatorsAssignorsRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.productHeaderRequestStatus.type,
				this.productsAssociatedOriginatorsAssignorsRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
		currentProductId(): number {
			return this.$route.params.id;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getProductHeader: 'getProductHeader',
			getProductsOriginatorsAssignors: 'getProductsOriginatorsAssignors',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getProductsOriginatorsAssignors({
				productId: this.currentProductId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		reload(): void {
			this.doSearch();
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.getProductsOriginatorsAssignors({
				productId: this.currentProductId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.productHeaderData.name,
				listIcons: [this.productHeaderData.id, this.productHeaderData.type],
			});
		},
		updatedListCards(): void {
			this.dataList = this.productsAssociatedOriginatorsAssignorsData.content;
		},
		updateTotalPages(): void {
			this.totalPages = this.productsAssociatedOriginatorsAssignorsData.totalPages;
		},
	},
	watch: {
		productHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
		productsAssociatedOriginatorsAssignorsRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedListCards();
				this.updateTotalPages();
			}
		},
	},
	mounted() {
		this.getProductHeader({
			idProduct: this.currentProductId,
		});
		this.doSearch();
	},
});
</script>
