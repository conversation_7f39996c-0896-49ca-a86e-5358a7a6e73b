<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="8">
					<CardTextBody
						ellipsisValue
						label="Data do Envio"
						:value="formatDateAndHours(data.createdAt)"
					/>
				</farm-col>
				<farm-col cols="4" align="right">
					<HistoricStatus :status="data.statusId" dense class="mr-2" />
					<farm-context-menu
						:items="contextMenuItems(data)"
						@details="handleDetails(data)"
						@download="handleDownload(data)"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody label="Nome do Arquivo" :value="data.filename" ellipsisValue />
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Usuário" :value="data.createdBy" ellipsisValue />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	download as downloadOption,
	details as detailsOption,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import HistoricStatus from '@/components/HistoricStatus';
import { formatDateAndHours } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		HistoricStatus,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	mounted() {
		downloadOption.label = 'Baixar';
		detailsOption.label = 'Ver Situação';
	},
	methods: {
		formatDateAndHours,
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [downloadOption, detailsOption];
		},
		handleDetails(data): void {
			this.$emit('handleDetails', data);
		},
		handleDownload(data): void {
			this.$emit('handleDownload', data);
		},
	},
});
</script>
