<template>
	<farm-row>
		<farm-col cols="12" md="4">
			<farm-label for="form-product-data-type" required> Tipo </farm-label>
			<farm-select-auto-complete
				id="form-product-data-type"
				item-text="type"
				item-value="id"
				v-model="form.type"
				:items="dataSelect"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-product-data-name" required> Nome </farm-label>
			<farm-textfield-v2
				uppercase
				v-model="nameValue"
				id="form-product-data-name"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-product-data-start-date" required> Data de início </farm-label>
			<farm-input-datepicker
				ref="datepickerStartDate"
				inputId="form-product-data-start-date"
				v-model="form.startDate"
				:required="true"
				:max="maxDate()"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4" v-if="isEdit">
			<farm-label for="form-product-data-end-date"> Data de fim</farm-label>
			<farm-input-datepicker
				ref="datepickerEndDate"
				inputId="form-product-data-end-date"
				v-model="form.endDate"
				:min="endDate(form.endDate)"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-product-data-status"> Status</farm-label>
			<farm-switcher
				class="mt-3"
				id="form-product-data-end-date"
				v-model="form.status"
				block
			></farm-switcher>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { RulesTypes } from '@/types';
import { ProductTabDataFormTypes, ProductTypeDataTypes } from '@/features/products/types';

type DataFormDataType = {
	rules: RulesTypes;
};

type SplitDateType = {
	year: number;
	month: number;
	day: number;
};

export default defineComponent({
	props: {
		form: {
			type: Object as PropType<ProductTabDataFormTypes>,
		},
		isEdit: {
			type: Boolean,
		},
		dataSelect: {
			type: Array as PropType<Array<ProductTypeDataTypes>>,
			default: () => [],
		},
	},
	data(): DataFormDataType {
		return {
			rules: {
				required: value => !!value || 'Campo obrigatório',
			},
		};
	},
	computed: {
		nameValue: {
			get() {
				return this.form.name;
			},
			set(value: string) {
				this.form.name = value.trim();
			}
		}
	},
	methods: {
		splitDate(date: string): SplitDateType {
			const [year, month, day] = date.split('-');
			return {
				year: parseInt(year, 10),
				month: parseInt(month, 10),
				day: parseInt(day, 10),
			};
		},
		endDate(dateInput: string): string {
			if (dateInput.length > 0) {
				const { year, month, day } = this.splitDate(dateInput);
				const dateAPI = new Date(year, month - 1, day);
				const dateNow = new Date();
				if (dateAPI > dateNow) {
					return new Date().toISOString();
				}
				return new Date(year, month - 1, day).toISOString();
			}
			return new Date().toISOString();
		},
		maxDate(): string {
			return new Date().toISOString();
		},
	},
});
</script>
