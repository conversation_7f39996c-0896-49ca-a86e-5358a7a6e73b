<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12">
				<TitlePageForm
					noPipe
					label="Histórico de Importações de Associações de "
					value="Sacados"
				/>
			</farm-col>
		</farm-row>
		<farm-row class="mt-1 mb-4 d-flex space-between align-end" v-if="!isError">
			<farm-col cols="12" sm="12" md="5" class="d-flex space-between">
				<div>
					<farm-label for="form-filtro-status"> Selecionar Status </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="selectFilter.status"
						:items="statusList"
						item-text="status"
						item-value="id"
					/>
				</div>
				<div>
					<farm-btn-confirm class="mt-8 ml-4" title="Buscar" @click="apply">
						Buscar
					</farm-btn-confirm>
				</div>
			</farm-col>
			<farm-col cols="12" sm="12" md="3"></farm-col>
			<farm-col cols="12" sm="12" md="4" align="end">
				<farm-select-auto-complete
					id="form-filtro-status"
					item-text="label"
					item-value="value"
					v-model="selectFilter.order"
					:items="orderList"
					@change="changeOrderBy"
				/>
			</farm-col>
		</farm-row>
		<HistoricList
			v-if="!isError"
			:data="dataList"
			@handleDetails="handleDetails"
			@handleDownload="handleDownload"
		/>
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<ModalFeedbackImportHistoric
			v-if="isModalFeedbackImport"
			v-model="isModalFeedbackImport"
			:data="dataImportHistoricSelected"
			@onClose="onCloseModal"
		/>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';
import ModalFeedbackImportHistoric from '@/components/ModalFeedbackImportHistoric';

import HistoricList from '../HistoricList';
import { historicSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		TitlePageForm,
		HistoricList,
		ModalFeedbackImportHistoric,
	},
	mixins: [pageable],
	data() {
		return {
			lastSearchFilters: {},
			filters: {
				page: 0,
				limit: 10,
				status: 0,
				type: 2,
			},
			selectFilter: {
				status: 0,
				order: 'createdAt_DESC',
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			totalPages: 1,
			statusList: [],
			dataList: [],
			dataSelected: null,
			orderList: sort,
			dataImportHistoricSelected: null,
			isModalFeedbackImport: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			importHistoricStatusDataRequestStatus: 'importHistoricStatusDataRequestStatus',
			importHistoricStatusData: 'importHistoricStatusData',
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
			batchImportHistoryList: 'batchImportHistoryList',
			batchImportHistoryRequestStatus: 'batchImportHistoryRequestStatus',
			batchImportHistoryById: 'batchImportHistoryById',
			batchImportHistoryByIdRequestStatus: 'batchImportHistoryByIdRequestStatus',
			downloadProductDraweeDocumentRequestStatus:
				'downloadProductDraweeDocumentRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.importHistoricStatusDataRequestStatus,
				this.productHeaderRequestStatus,
				this.batchImportHistoryRequestStatus,
				this.batchImportHistoryByIdRequestStatus,
				this.downloadProductDraweeDocumentRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.importHistoricStatusDataRequestStatus.type,
				this.batchImportHistoryRequestStatus.type,
				this.batchImportHistoryByIdRequestStatus.type,
				this.productHeaderRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return this.$route.params.id;
		},
		currentType(): string {
			return this.productHeaderData?.type.toLowerCase();
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
	},
	mounted(): void {
		this.getProductHeader({
			idProduct: this.currentId,
		});
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getImportHistoricStatus: 'getImportHistoricStatus',
			getProductHeader: 'getProductHeader',
			getBatchImportHistory: 'getBatchImportHistory',
			getBatchImportHistoryById: 'getBatchImportHistoryById',
			downloadProductDraweeDocument: 'downloadProductDraweeDocument',
		}),
		changeOrderBy(): void {
			const [orderby, order] = this.selectFilter.order.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};

			this.doSearch();
		},
		updateList(): void {
			this.dataList = this.batchImportHistoryList.content;
		},
		updateTotalPages(): void {
			this.totalPages = this.batchImportHistoryList.totalPages;
		},
		doSearch(): void {
			this.getImportHistoricStatus();
			this.lastSearchFilters = { ...this.filters };
			this.getBatchImportHistory({
				filters: { ...this.filters, ...this.hasSort, productId: this.currentId },
			});
		},
		apply(): void {
			this.filters = { ...this.filters, status: this.selectFilter.status };
			this.doSearch();
		},
		updatedHistoricStatus(): void {
			this.statusList = [
				{ id: 0, status: 'Todos' },
				...this.importHistoricStatusData.content,
			];
		},
		handleDetails(data): void {
			const payload = {
				id: data.id,
			};
			this.getBatchImportHistoryById(payload);
		},
		currentSelected(): void {
			this.dataImportHistoricSelected = { ...this.batchImportHistoryById.content };
		},
		handleDownload(data): void {
			notification(RequestStatusEnum.SUCCESS, {
				message: `O download do arquivo iniciará em uma nova aba.`,
				title: 'Download',
			});
			setTimeout(() => {
				this.downloadProductDraweeDocument({ path: data.path });
			}, 1000);
		},
		onCloseModal(): void {
			this.isModalFeedbackImport = false;
		},
		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.productHeaderData.name,
				listIcons: [this.productHeaderData.id, this.productHeaderData.type],
			});
		},
	},
	watch: {
		importHistoricStatusDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHistoricStatus();
			}
		},
		productHeaderRequestStatus(newValue: string) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
				this.doSearch();
			}
		},
		batchImportHistoryRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updateList();
				this.updateTotalPages();
			}
		},
		batchImportHistoryByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.currentSelected();
				this.isModalFeedbackImport = true;
			}
		},
	},
});
</script>
