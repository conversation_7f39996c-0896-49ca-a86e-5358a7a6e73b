<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndDocument" noSpacing class="mb-1" />
					<CardListTextHeader :data="type" noSpacing />
				</farm-col>
				<farm-col cols="3" align="end">
					<CardContextMenu
						:showMenu="data.contextMenu"
						:disabledMenu="!data.contextMenu"
						:items="contextMenuItems(data)"
						@finalizeAssociation="handleFinalizeAssociation(data)"
						@reAssociate="handleReAssociateOption(data)"
						@edit="handleEdit(data)"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="4">
					<CardTextBody label="Cessão" :value="formatYesOrNo(data.assignment)" />
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Emissão de Lastro"
						:value="formatYesOrNo(data.emissionOfBallast)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody label="Originação" :value="formatYesOrNo(data.origination)" />
				</farm-col>
			</farm-row>
			<farm-row y-grid-gutters>
				<farm-col cols="4">
					<CardTextBody
						label="Percentual de Concentração"
						ellipsisLabel
						:value="parseConcentrationPercentage(data.concentrationPercentage)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Fim de Associação"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	edit as editOption,
	reAssociate as reAssociateOption,
	finalizeAssociation as finalizeAssociationOption,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardContextMenu from '@/components/CardContextMenu';

import { parseConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import { formatYesOrNo, formatDateOrNA } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTitleHeader,
		CardListTextHeader,
		CardTextBody,
		CardContextMenu,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatYesOrNo,
			formatDateOrNA,
			parseConcentrationPercentage,
			type: [{ label: 'Tipo', value: this.data.type, copyText: '' }],
			listIdAndDocument: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{
					label: 'CNPJ',
					value: this.data.document,
					copyText: this.data.document,
					successMessage: 'Documento copiado para área de transferência!',
				},
			],
		};
	},
	methods: {
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			if (this.data.endRelationship === null) {
				return [editOption, finalizeAssociationOption];
			} else {
				return [reAssociateOption];
			}
		},
		handleEdit(item): void {
			this.$emit('handleEdit', item);
		},
		handleFinalizeAssociation(item): void {
			this.$emit('handleFinalizeAssociation', item);
		},
		handleReAssociateOption(item): void {
			this.$emit('handleReAssociateOption', item);
		},
	},
});
</script>
