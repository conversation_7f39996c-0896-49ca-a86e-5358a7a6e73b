<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col
				cols="12"
				sm="12"
				md="6"
				v-for="(item, index) in data"
				:key="`drawee-card-${item.id}-${index}`"
			>
				<DraweeCard :data="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

interface IListItems {
	id: number;
	name: string;
	type: string;
	status: string;
	taxonomyWallet: string;
	classification: string;
	startRelationship: Date;
	endRelationship: Date;
	onboarding: number;
	concentrationPercentage: string;
	mandateSignature: number;
	relationshipDate: string;
	releasedSource: number;
	membership: number;
}

import DraweeCard from '../DraweeCard';

export default defineComponent({
	components: {
		DraweeCard,
	},
	props: {
		data: {
			type: Array as PropType<IListItems[]>,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
});
</script>
