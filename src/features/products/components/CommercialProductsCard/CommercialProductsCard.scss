ul {
	display: flex;
	flex-direction: column;
	list-style: none;
	padding: 0;
	margin: 0;
}

li {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	gap: 8px;
	width: 100%;
	position: relative;

	&:not(:last-child):after {
		content: "";
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 1px;
		background-color: var(--farm-gray-lighten);
	}

	>div {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 8px;
	}
}

li:not(:first-child) {
	padding-top: 16px;
}

li:not(:last-child) {
	padding-bottom: 16px;
}

.clickHere {
	font-weight: 700;
	color: var(--farm-primary-base);
	cursor: pointer;

	&:hover {
		text-decoration: underline;
	}
}
