<template>
	<farm-box>
		<Cards>
			<template slot="header">
				<farm-row class="d-flex space-between align-center">
					<farm-col cols="9">
						<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
						<CardListTextHeader :data="dataHeader" noSpacing class="mb-1" />
					</farm-col>
					<farm-col cols="3" align="end">
						<div class="d-flex justify-end align-center">
							<StatusActiveAndInactive
								:status="status[data.status]"
								dense
								class="mr-2"
							/>
							<farm-context-menu
								v-if="onContextMenuItems(data).length"
								:items="onContextMenuItems(data)"
								@edit="onEditItem(data)"
								@reAssociate="onReAssociateOption(data)"
								@finalizeAssociation="onFinalizeAssociation(data)"
							/>
						</div>
					</farm-col>
				</farm-row>
			</template>
			<template slot="body">
				<farm-box>
					<ul>
						<template v-if="!parametersNotEnabled">
							<li v-for="(item, index) in data.modality" :key="index">
								<div>
									<img
										src="@/assets/icons/currency_exchange.svg"
										alt="imagem referente a dinheiro"
									/>
									<farm-typography weight="700" size="16px" tag="h3"
										>Modalidades de
										{{ getNameByIdModality(item.modalityId) }}</farm-typography
									>
								</div>
								<CardListTextHeader
									:data="createListCardHeaderType(item.settlementTypes)"
									noSpacing
									class="mb-1"
								/>
							</li>
						</template>
						<template v-if="!parametersNotEnabled">
							<li v-if="data.financialVehicles.length">
								<div>
									<img
										src="@/assets/icons/bank.svg"
										alt="imagem referente a dinheiro"
									/>
									<farm-typography weight="700" size="16px" tag="h3"
										>Veículos Financeiros</farm-typography
									>
								</div>
								<CardListTextHeader
									v-for="(item, index) in data.financialVehicles"
									:data="listFinancialVehicle(item)"
									noSpacing
									class="mb-1"
									:key="index"
								/>
							</li>
						</template>
						<li v-if="parametersNotEnabled">
							<div>
								<img
									src="@/assets/icons/warning.svg"
									alt="imagem referente a dinheiro"
								/>
								<farm-typography weight="700" size="16px" tag="h3"
									>Sem modalidades habilitadas e/ou veículos financeiros
									associados</farm-typography
								>
							</div>
							<farm-typography
								color="gray"
								colorVariation="darken"
								weight="400"
								size="14px"
								tag="p"
								>Para finalizar ou alterar a parametrização deste produto comercia
								<span @click="goToMenuCommercialProduc" class="clickHere"
									>clique aqui</span
								></farm-typography
							>
						</li>
						<li v-if="nonParameterizedAssociation">
							<div>
								<img
									src="@/assets/icons/warning.svg"
									alt="imagem referente a dinheiro"
								/>
								<farm-typography weight="700" size="16px" tag="h3"
									>Associação não parametrizada</farm-typography
								>
							</div>
							<farm-typography
								color="gray"
								colorVariation="darken"
								weight="400"
								size="14px"
								tag="p"
								>Realize novamente as parametrizações de associação</farm-typography
							>
						</li>
					</ul>
					<farm-prompt-user
						v-model="isShowModal"
						title="Finalizar Associação"
						subtitle=""
						match="REMOVER"
						@onConfirm="onConfirm"
						@onClose="onClose"
					>
						<template v-slot:subtitle>
							<farm-typography size="md" class="mt-6">
								Deseja realmente finalizar a associação com o Produto Comercial
								<b>{{ dataSelected.name }}</b
								>?
							</farm-typography>
							<farm-typography size="md" class="mt-3">
								Escreva no campo abaixo
								<farm-typography bold size="md" tag="span"
									>“REMOVER”</farm-typography
								>
								para confirmar o fim da associação.
							</farm-typography>
						</template>
					</farm-prompt-user>
				</farm-box>
			</template>
		</Cards>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, toRefs, ref } from 'vue';
import {
	edit as editOption,
	RequestStatusEnum,
	notification,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardContextMenu from '@/components/CardContextMenu';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import { CommercialProduct } from '../../types';
import { parseConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import CardTextBody from '@/components/CardTextBody/CardTextBody.vue';
import { formatDateOrNA } from '@/helpers/formatCards';
import { useRouter } from '@/composibles';
import { useAssociationCommercialProduct } from '../../composables/useAssociationCommercialProduct';
import { useAssociationCommercialProductMessage } from '../../composables/useAssociationCommercialProductMessage';
import { useRedirectCommercialProduct } from '../../composables/useRedirectCommercialProduct';
import { useAssociationCommercialProductEditMessage } from '../../composables/useAssociationCommercialProductEditMessage';

export default defineComponent({
	name: 'commercial-products-card',
	components: {
		Cards,
		CardTitleHeader,
		CardListTextHeader,
		CardTextBody,
		CardContextMenu,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object as PropType<CommercialProduct>,
			default: null,
		},
		settlementTypeList: {
			type: Array,
			default: () => [],
		},
		modalityList: {
			type: Array,
			default: () => [],
		},
	},
	setup(props) {
		const { data, settlementTypeList, modalityList } = toRefs(props);
		const { redirectMenuCommercialProductById, redirectEditCommercialProduct } =
			useRedirectCommercialProduct();
		const { confirmReAssociation } = useAssociationCommercialProductMessage();
		const { associationCommercialProduct, isLoadingAssociationCommercialProduct } =
			useAssociationCommercialProduct();
		const { confirmEditAssociation } = useAssociationCommercialProductEditMessage();

		const status = {
			0: 'INATIVO',
			1: 'ATIVO',
		};
		const dataSelected = ref(null);

		const terminateAssociationOption = ref({
			label: 'Finalizar associação',
			handler: 'finalizeAssociation',
			icon: { color: 'error', type: 'close-circle-outline' },
		});
		const reAssociate = ref({
			label: 'Associar Novamente',
			handler: 'reAssociate',
			icon: { color: 'primary', type: 'reload' },
		});

		const isShowModal = ref(false);

		function onClose(): void {
			isShowModal.value = false;
		}

		const isLoading = computed(() => {
			return isLoadingAssociationCommercialProduct.value;
		});

		const parametersNotEnabled = computed(() => {
			return (
				(data.value.financialVehicles.length === 0 || data.value.modality.length === 0) &&
				!data.value.misconfigured
			);
		});

		const router = useRouter();

		const nonParameterizedAssociation = computed(() => {
			return data.value.misconfigured;
		});

		function createListCardHeader(item) {
			const label = item.status === 1 ? 'Associado em' : 'Fim de Associação';
			const value =
				item.status === 1
					? formatDateOrNA(item.associationStart)
					: formatDateOrNA(item.associationEnd);
			return [
				{ label: 'ID', value: item.id, copyText: '' },
				{
					label,
					value,
					copyText: '',
				},
			];
		}

		function listFinancialVehicle(item) {
			return [
				{ label: '', value: item.financialVehicle, copyText: '' },
				{
					label: 'Percentual de Concentração',
					value: parseConcentrationPercentage(item.percentage, '', '%'),
					copyText: '',
				},
			];
		}

		function createListCardHeaderType(item) {
			return item.map(item => {
				return {
					label: '',
					value: getSettlementType(item.settlementTypeId),
					copyText: '',
				};
			});
		}

		const dataHeader = computed(() => {
			return createListCardHeader(data.value);
		});

		function getNameByIdModality(modalityId) {
			const modality = modalityList.value.find(item => item.id === modalityId);
			return modality?.name || 'N/A';
		}

		function getSettlementType(settlementTypeId) {
			const settlementType = settlementTypeList.value.find(
				item => item.id === settlementTypeId
			);
			return settlementType?.name || 'N/A';
		}

		function goToMenuCommercialProduc() {
			redirectMenuCommercialProductById(data.value.id);
		}

		function onContextMenuItems(data) {
			if (data.status === 0) {
				return [reAssociate.value];
			}
			return [editOption, terminateAssociationOption.value];
		}

		function onEditItem(item): void {
			const id = router.currentRoute.params.id;
			redirectEditCommercialProduct(id, item.id, item.name);
		}

		function redirectToInit() {
			const id = router.currentRoute.params.id;
			router.push(`/admin/cadastros/produtos/${id}/editar?path=produtos_comerciais`);
			router.go(0);
		}

		function updatedPage(payloadCache?, ResquestStatus?: string): void {
			const messageFinish = `Associação finalizada com sucesso para o Produto Comercial <b>${payloadCache.name}</b>!`;
			const id = router.currentRoute.params.id;
			if (ResquestStatus === RequestStatusEnum.SUCCESS) {
				if (payloadCache.isFinish) {
					notification(RequestStatusEnum.SUCCESS, messageFinish);
					setTimeout(() => {
						redirectToInit();
					}, 1500);
				} else {
					confirmEditAssociation(id, dataSelected.value.id, dataSelected.value.name);
				}
			} else {
				redirectToInit();
			}
		}

		function callReAssociate(): void {
			const id = router.currentRoute.params.id;
			const payload = {
				id,
				commercialProductId: dataSelected.value.id,
				payload: {
					enabled: 1,
				},
			};
			associationCommercialProduct(payload, updatedPage);
		}

		function onConfirm(): void {
			const id = router.currentRoute.params.id;
			const payload = {
				id,
				commercialProductId: dataSelected.value.id,
				name: dataSelected.value.name,
				payload: {
					enabled: 0,
				},
			};

			associationCommercialProduct(payload, updatedPage);
			isShowModal.value = false;
		}

		function onReAssociateOption(data): void {
			dataSelected.value = data;
			confirmReAssociation(dataSelected.value.name, callReAssociate);
		}

		function onFinalizeAssociation(data): void {
			dataSelected.value = data;
			isShowModal.value = true;
		}

		return {
			data,
			status,
			isShowModal,
			dataSelected,
			dataHeader,
			isLoading,
			parametersNotEnabled,
			nonParameterizedAssociation,
			getNameByIdModality,
			createListCardHeaderType,
			parseConcentrationPercentage,
			listFinancialVehicle,
			goToMenuCommercialProduc,
			onContextMenuItems,
			onEditItem,
			onReAssociateOption,
			onFinalizeAssociation,
			onClose,
			onConfirm,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CommercialProductsCard.scss';
</style>
