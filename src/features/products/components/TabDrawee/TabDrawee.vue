<template>
	<farm-box>
		<farm-row justify="space-between" align="center" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Sacado"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<ButtonImport
					label="Importar Associações"
					color="primary"
					icon
					outlined
					:options="options"
					@onClick="handleClick"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<DraweeFilter v-show="filter" key="filters" @onApply="searchListener" />
		</collapse-transition>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="8" md="8"></farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<DraweeList v-if="!isError" :data="dataList" />
		<farm-row extra-decrease v-if="!isError && dataList.length > 0">
			<farm-box>
				<farm-datatable-paginator
					class="mt-6"
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { format } from '@/helpers/formatUpdateUser';
import ButtonImport from '@/components/ButtonImport/index';
import { createObject } from '@/helpers/createObject';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';

import DraweeFilter from '../DraweeFilter';
import DraweeList from '../DraweeList';
import { draweeSort as sort } from '../../configurations/sorts';
import { buttonImportOptions } from '../../configurations/buttonImportOptions';
import { mapActions, mapGetters } from 'vuex';

export default defineComponent({
	mixins: [pageable],
	components: { FooterForm, ButtonImport, DraweeFilter, DraweeList },
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			filterInputKey: 'search',
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataList: [],
			totalPages: 0,
			options: buttonImportOptions,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			associatedDraweeToProductsList: 'associatedDraweeToProductsList',
			associatedDraweeToProductsRequestStatus: 'associatedDraweeToProductsRequestStatus',
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.associatedDraweeToProductsRequestStatus,
				this.productHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.associatedDraweeToProductsRequestStatus.type,
				this.productHeaderRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getAssociatedDraweeToProducts: 'getAssociatedDraweeToProducts',
			getProductHeader: 'getProductHeader',
		}),
		updateList(): void {
			this.dataList = this.associatedDraweeToProductsList.content;
		},
		updateTotalPages(): void {
			this.totalPages = this.associatedDraweeToProductsList.totalPages;
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		doSearch(): void {
			const productId = this.currentId;
			this.lastSearchFilters = { ...this.filters };

			this.getAssociatedDraweeToProducts({
				productId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},

		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},

		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.productHeaderData.name,
				listIcons: [this.productHeaderData.id, this.productHeaderData.type],
			});
		},
		updatedDateFooter(): void {
			const updatedFooterFormData = format(this.associatedDraweeToProductsList.meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},

		handleClick(url: string): void {
			this.$router.push({
				path: `/admin/cadastros/produtos/${this.currentId}/${url}`,
			});
		},
	},
	watch: {
		associatedDraweeToProductsRequestStatus(newValue: string) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updateList();
				this.updateTotalPages();
				this.updatedDateFooter();
			}
		},
		productHeaderRequestStatus(newValue: string) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
	},

	mounted() {
		this.getProductHeader({
			idProduct: this.currentId,
		});
		this.doSearch();
	},
});
</script>
