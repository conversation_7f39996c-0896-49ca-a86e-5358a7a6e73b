<template>
	<farm-row>
		<farm-col cols="12">
			<TitlePageForm noPipe label="Importação de Associações de " value="Sacados" />
		</farm-col>
		<farm-col cols="12" class="d-flex align-center flex-wrap">
			<farm-bodytext class="mb-0" variation="regular" :type="2">
				Não possui o arquivo modelo?
			</farm-bodytext>
			<farm-btn
				class="ml-0 px-0 px-sm-2 ml-sm-4"
				color="primary"
				title="Baixar Planilha Modelo"
				plain
				@click="downloadTemplate"
			>
				<farm-icon class="mr-2">download</farm-icon>
				Baixar Planilha Modelo
			</farm-btn>
		</farm-col>
		<farm-col cols="12">
			<farm-alertbox
				class="mt-6"
				icon="alert-circle"
				color="info"
				v-if="!filePickerMaxSizeWarning && !filePickerInvalid"
			>
				Envie arquivos no formato: .xls, .xlsx
			</farm-alertbox>
			<farm-alertbox
				class="mt-6"
				icon="alert-circle"
				color="info"
				v-if="filePickerMaxSizeWarning"
			>
				Você excedeu o limite permitido de 10mb. Envie arquivos de até 10mb para prosseguir
				com a solicitação.
			</farm-alertbox>
			<farm-alertbox class="mt-6" icon="alert-circle" color="info" v-if="filePickerInvalid">
				O arquivo não pode ser enviado. Envie arquivos no formato: .xls, .xlsx
			</farm-alertbox>
		</farm-col>
		<farm-col cols="12" class="mt-6">
			<farm-multiple-filepicker
				:maxFileSize="10"
				:maxFilesNumber="1"
				acceptedFileTypes="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
				@onMaxFileSizeWarning="showMaxSizeWarning"
				@onFileChange="updateFiles"
				@onInvalidFiles="onInvalidFiles"
			/>
		</farm-col>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import { RequestStatusEnum, notification, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';

export default defineComponent({
	components: {
		TitlePageForm,
	},
	data() {
		return {
			file: null,
			filePickerMaxSizeWarning: false,
			filePickerInvalid: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
			saveProductDraweeImportRequestStatus: 'saveProductDraweeImportRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.productHeaderRequestStatus,
				this.saveProductDraweeImportRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	mounted(): void {
		this.getProductHeader({
			idProduct: this.currentId,
		});
		this.$emit('onSubmit', this.save);
	},
	methods: {
		...mapActions('cadastros', {
			getProductHeader: 'getProductHeader',
			downloadDraweeBatchTemplate: 'downloadDraweeBatchTemplate',
			saveProductDraweeImport: 'saveProductDraweeImport',
		}),
		updateFiles(newFile: File): void {
			this.file = newFile;
			const hasFile = this.file.length > 0;
			if (hasFile) {
				this.resetted();
			}
			this.$emit('onDisabledButtonFooter', hasFile);
		},
		resetted(): void {
			this.filePickerInvalid = false;
			this.filePickerMaxSizeWarning = false;
		},
		showMaxSizeWarning(): void {
			this.filePickerMaxSizeWarning = true;
		},
		onInvalidFiles(): void {
			this.filePickerInvalid = true;
		},
		downloadTemplate(): void {
			notification(RequestStatusEnum.SUCCESS, {
				message: `O download do arquivo será iniciado em uma nova aba.`,
				title: 'Download',
			});
			setTimeout(() => {
				this.downloadDraweeBatchTemplate();
			}, 1000);
		},
		save(): void {
			this.createDialog();
		},
		createDialog() {
			this.$dialog
				.confirm(
					{
						title: 'Confirmação de Importação',
						body: `
						<b style="display: flex; color: var(--farm-warning-base)">
							<i class="mdi mdi-alert"></i>
							<p style="margin-left: 8px;">
								Leia com atenção antes de finalizar:
							</p>
						</b>
						<br>

						A importação em lote de associações será feita para o <br> produto <b>${stripTags(
							this.productHeaderData.name
						)}</b> e essa ação é irreversível. <br> <br>
						Clique em <b>“Concluir”</b> para realizar a importação ou em <br> <b>“Cancelar”</b> para interromper.`,
					},
					{
						html: true,
						okText: 'Concluir',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const formData = new FormData();
					formData.append('file', this.file[0], this.file[0].name);

					this.saveProductDraweeImport({ formData, productId: this.currentId });
				});
		},

		createTransitionDialog() {
			this.$dialog
				.confirm(
					{
						title: 'Importação em Lote',
						body: `Importação em andamento! <br> Deseja acompanhar a  importação?`,
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.$emit('changeRouter');
				})
				.catch(() => {
					this.$router.push({
						path: `/admin/cadastros/produtos/${this.currentId}/editar?path=sacados`,
					});
				});
		},

		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.productHeaderData.name,
				listIcons: [this.productHeaderData.id, this.productHeaderData.type],
			});
		},
	},
	watch: {
		saveProductDraweeImportRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.$emit('onDisabledButtonFooter', false);
				this.createTransitionDialog();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, 'Não foi possivel importar a planilha');
				this.resetted();
				this.file = [];
				this.$emit('onDisabledButtonFooter', false);
			}
		},
		productHeaderRequestStatus(newValue: string) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
	},
});
</script>
