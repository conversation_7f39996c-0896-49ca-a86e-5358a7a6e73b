<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader value="TESTE" class="mb-1" ellipsis />
					<CardListTextHeader :data="[]" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense class="mr-2" />
						<farm-context-menu :items="[]" />
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="8">
					<CardTextBody ellipsisValue label="Tipo" value="TESTE 2" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardContextMenu from '@/components/CardContextMenu';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

export default defineComponent({
	name: 'financial-vehicles-card',
	components: {
		Cards,
		CardTitleHeader,
		CardListTextHeader,
		CardTextBody,
		CardContextMenu,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup() {
		return {};
	},
});
</script>
