<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="10">
					<CardTitleHeader :value="data.name" ellipsis />
				</farm-col>
				<farm-col cols="2" align="end">
					<farm-context-menu
						:items="contextMenuItems(data)"
						@edit="handleEdit(data)"
						@remove="handleRemove(data)"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="4">
					<farm-idcaption
						copyText=""
						:link="true"
						@onLinkClick="handleOpenModalDrawee(data)"
					>
						<template v-slot:title>
							<farm-caption variation="regular" color="gray"> Sacados </farm-caption>
						</template>
						<template v-slot:subtitle>
							<farm-bodytext
								variation="bold"
								color="neutral"
								color-variation="darken"
								:type="2"
							>
								{{ data.draweeCount }}
							</farm-bodytext>
						</template>
					</farm-idcaption>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody label="Data de Criação" :value="formatDateOrNA(data.createdAt)" />
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Última Atualização"
						:value="formatDateOrNA(data.updatedAt)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { edit as editOption, remove as removeOption } from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardTextBody from '@/components/CardTextBody';
import { formatDateOrNA } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTitleHeader,
		CardTextBody,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatDateOrNA,
		};
	},
	methods: {
		contextMenuItems(item) {
			if (!this.canWrite) {
				return [];
			}
			if (parseInt(item.draweeCount, 10) === 0) {
				return [editOption, removeOption];
			}
			return [editOption];
		},
		handleEdit(item): void {
			this.$emit('onClickEdit', item);
		},
		handleRemove(item): void {
			this.$emit('onClickRemove', item);
		},
		handleOpenModalDrawee(item) {
			this.$emit('openModalDrawee', item);
		},
	},
});
</script>
