<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="formatValueOrNA(data.name)" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndType" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense />
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="4">
					<CardTextBody
						label="Carteira"
						:value="formatValueOrNA(data.taxonomyWallet)"
						ellipsisValue
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Classificação"
						:value="formatValueOrNA(data.classification)"
						ellipsisValue
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody label="Onboarding" :value="formatYesOrNo(data.onboarding)" />
				</farm-col>
			</farm-row>
			<farm-row y-grid-gutters>
				<farm-col cols="4">
					<CardTextBody
						label="Adesão ao Programa"
						:value="formatYesOrNo(data.membership)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Liberação Originador"
						:value="formatYesOrNo(data.releasedSource)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Assinatura Mandato"
						:value="formatMandateSignature(data.mandateSignature)"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="4">
					<CardTextBody
						label="Data de Relacionamento"
						:value="formatDateOrNA(data.relationshipDate)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Fim de Associação"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
			</farm-row>
			<farm-row class="mt-3">
				<farm-col cols="5">
					<CardTextBody
						label="Percentual de Concentração"
						:value="formatConcentrationPercentage(data.concentrationPercentage)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatValueOrNA,
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
			formatConcentrationPercentage,
			listIdAndType: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{ label: 'Raiz', value: this.data.raiz, copyText: this.data.raiz },
				{ label: 'Tipo de Pessoa', value: this.data.peopleType, copyText: '' },
			],
		};
	},
});
</script>
