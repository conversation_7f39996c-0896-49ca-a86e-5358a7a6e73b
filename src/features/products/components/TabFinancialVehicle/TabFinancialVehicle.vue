<template>
	<farm-box v-if="!isError">
		<farm-row v-if="isWholesale">
			<farm-col cols="12" class="pb-5">
				<farm-alertbox icon="alert-circle" color="info" dense>
					Produtos do tipo <b>atacado</b> não podem ter mais de uma associação ativa com
					<b>veículos financeiros</b>.
				</farm-alertbox>
			</farm-col>
		</farm-row>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Veículo Financeiro"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					class="mt-8"
					customIcon="plus"
					title="Associar Veículo Financeiro"
					:icon="true"
					:disabled="disabledButton"
					@click="handleNewAssociation"
				>
					Associar Veículo Financeiro
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<FinancialVehicleFilter
				key="filters"
				v-show="filter"
				:listType="dataFilter"
				@onApply="searchListener"
			/>
		</collapse-transition>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<FinancialVehicleList
			v-if="!isError"
			:data="dataCard"
			@handleEdit="handleEdit"
			@handleFinalizeAssociation="handleFinalizeAssociation"
			@handleReAssociateOption="handleReAssociateOption"
		/>
		<farm-row extra-decrease v-if="!isError && dataCard.length > 0">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-prompt-user
			v-model="showModal"
			title="Finalizar Associação"
			subtitle=""
			match="REMOVER"
			@onConfirm="onConfirm"
			@onClose="onClose"
		>
			<template v-slot:subtitle>
				<farm-typography size="md" class="mt-6">
					Deseja realmente finalizar a associação com o veículo financeiro
				</farm-typography>
				<farm-typography bold size="md"> {{ dataSelected.name }}?</farm-typography>
				<farm-typography size="md" class="mt-3">
					Escreva no campo abaixo
					<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography> para
					confirmar o fim da associação.
				</farm-typography>
			</template>
		</farm-prompt-user>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	pageable,
	notification,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import {
	parse,
	hasFinancialVehiclesAssociation,
} from '@/helpers/rulesProductAssociationFinancialVehicles';

import FinancialVehicleFilter from '../FinancialVehicleFilter';
import FinancialVehicleList from '../FinancialVehicleList';
import { WHOLESALE } from '../../constants';
import { financialSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		FinancialVehicleFilter,
		FinancialVehicleList,
	},
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			filterInputKey: 'search',
			dataFilter: [],
			dataCard: [],
			totalPages: 0,
			dataSelected: null,
			showModal: false,
			disabledButton: true,
			isWholesale: false,
		};
	},
	props: {
		data: {
			type: Object,
			default: () => null,
		},
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehiclesRequestStatus: 'financialVehiclesRequestStatus',
			financialVehiclesList: 'financialVehiclesList',
			financialVehicleTypes: 'financialVehicleTypes',
			financialVehicleTypesRequestStatus: 'financialVehicleTypesRequestStatus',
			deleteFinancialVehicleFromProductRequestStatus:
				'deleteFinancialVehicleFromProductRequestStatus',
			reassociateFinancialVehicleToProductRequestStatus:
				'reassociateFinancialVehicleToProductRequestStatus',
			productHeaderData: 'productHeaderData',
			productHeaderRequestStatus: 'productHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatuses = [
				this.financialVehiclesRequestStatus,
				this.financialVehicleTypesRequestStatus,
				this.reassociateFinancialVehicleToProductRequestStatus,
				this.deleteFinancialVehicleFromProductRequestStatus,
				this.getProductHeaderRequestStatus,
			];
			return requestStatuses.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			return this.financialVehiclesRequestStatus.type === RequestStatusEnum.ERROR;
		},
		currentProductId(): number {
			return this.$route.params.id;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicles: 'getFinancialVehicles',
			getFinancialVehicleTypes: 'getFinancialVehicleTypes',
			reassociateFinancialVehicleToProduct: 'reassociateFinancialVehicleToProduct',
			getProductHeader: 'getProductHeader',
			deleteFinancialVehicleFromProduct: 'deleteFinancialVehicleFromProduct',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getFinancialVehicles({
				filters: { ...this.filters, ...this.hasSort },
				id: this.currentProductId,
			});
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		reload(): void {
			this.doSearch();
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		onConfirm(): void {
			const payload = {
				productId: this.currentProductId,
				financialVehicleId: this.dataSelected.id,
			};
			this.deleteFinancialVehicleFromProduct(payload);
			this.onClose();
		},
		onClose(): void {
			this.showModal = false;
		},
		reloadPage(): void {
			setTimeout(() => {
				this.doSearch();
			}, 2000);
		},
		updatedList(): void {
			const { content, alreadyInAssociation } = this.financialVehiclesList;
			this.dataCard = parse(content, alreadyInAssociation, this.isWholesale);
		},
		updatedTotalPages(): void {
			this.totalPages = this.financialVehiclesList.totalPages;
		},
		checkDisabledButtonNewAssociation(): void {
			const { alreadyInAssociation } = this.financialVehiclesList;
			this.disabledButton = hasFinancialVehiclesAssociation(
				this.dataCard,
				alreadyInAssociation,
				this.isWholesale
			);
		},
		handleEdit(data): void {
			const financialVehicleId = data.id;
			const path = `/admin/cadastros/produtos/${this.currentProductId}/editar/${financialVehicleId}/associacao_veiculo_financeiro`;
			this.$router.push({
				path,
			});
		},
		handleNewAssociation(): void {
			const path = `/admin/cadastros/produtos/${this.currentProductId}/associacao_veiculo_financeiro/novo`;
			this.$router.push({
				path,
			});
		},
		handleFinalizeAssociation(data): void {
			this.dataSelected = data;
			this.showModal = true;
		},
		updatedHeader(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [data.id, data.type],
			});
		},
		handleReAssociateOption(data) {
			this.dataSelected = data;
			this.$dialog
				.confirm(
					{
						body: `Deseja associar novamente o veículo financeiro <b>${stripTags(
							data.name
						)}</b>? `,
						title: 'Associar Novamente',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const payload = {
						productId: this.currentProductId,
						financialVehicleId: this.dataSelected.id,
					};
					this.reassociateFinancialVehicleToProduct(payload);
				})
				.catch(() => {});
		},
		createDialogEdit() {
			this.$dialog
				.confirm(
					{
						body: `Produto associado com sucesso ao veículo financeiro <b>${stripTags(
							this.dataSelected.name
						)}</b>!<br/>Deseja editar associação? `,
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const financialVehicleId = this.dataSelected.id;
					const path = `/admin/cadastros/produtos/${this.currentProductId}/editar/${financialVehicleId}/associacao_veiculo_financeiro`;
					this.$router.push({
						path,
					});
				})
				.catch(() => {
					this.doSearch();
				});
		},
	},

	watch: {
		financialVehiclesRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedTotalPages();
				this.checkDisabledButtonNewAssociation();
			}
		},
		financialVehicleTypesRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataFilter = this.financialVehicleTypes;
			}
		},

		deleteFinancialVehicleFromProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					`Associação finalizada com sucesso para o veículo financeiro <b>${this.dataSelected.name}</b>!`
				);
				this.reloadPage();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível finalizada a associação deste produto <b>${this.dataSelected.name}</b>.`
				);
			}
		},
		reassociateFinancialVehicleToProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.createDialogEdit();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível associar o veículo financeiro <b>${this.dataSelected.name}</b>.`
				);
			}
		},
		productHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.productHeaderData);
				this.isWholesale = this.productHeaderData.type === WHOLESALE;
				this.doSearch();
			}
		},
	},
	mounted(): void {
		this.getFinancialVehicleTypes();
		const payload = {
			idProduct: this.currentProductId,
		};
		this.getProductHeader(payload);
	},
	mixins: [pageable],
});
</script>
