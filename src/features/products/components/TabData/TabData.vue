<template>
	<farm-box>
		<farm-form v-model="valid" v-if="!isError">
			<DataForm :form="form" :isEdit="isEdit" :dataSelect="dataSelect" />
		</farm-form>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT, NEW } from '@/constants';
import { format } from '@/helpers/formatUpdateUser';
import { createObject, createArray } from '@/helpers/createObject';
import { mapToDataForm, createPayload } from '@/helpers/productForm';

import { ProductTabDataFormTypes, productTabDataFormModel, ProductDataTypes } from '../../types';

import DataForm from '../DataForm';

type ProductTabDataDataTypes = {
	valid: boolean;
	form: ProductTabDataFormTypes;
	dataSelect: Array<ProductDataTypes>;
	postCreatedProductId: string;
};
type MessageTypes = 'ERROR' | 'SUCCESS';

export default defineComponent({
	props: {
		isEdit: {
			type: Boolean,
		},
	},
	components: {
		DataForm,
	},
	data(): ProductTabDataDataTypes {
		return {
			valid: false,
			form: createObject<ProductTabDataFormTypes>(productTabDataFormModel),
			dataSelect: createArray<ProductDataTypes>([]),
			postCreatedProductId: '',
		};
	},
	mounted(): void {
		this.load();
		this.$emit('onSubmit', this.submit);
	},
	computed: {
		...mapGetters('cadastros', {
			productTypeData: 'productTypeData',
			productByIdData: 'productByIdData',
			productTypeDataRequestStatus: 'productTypeDataRequestStatus',
			productByIdDataRequestStatus: 'productByIdDataRequestStatus',
			saveProductsRequestStatus: 'saveProductsRequestStatus',
			productPostCreatedId: 'productPostCreatedId',
		}),
		isLoading(): boolean {
			const status = [
				this.productTypeDataRequestStatus,
				this.productByIdDataRequestStatus,
				this.saveProductsRequestStatus,
			];
			return status.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const status = [
				this.productTypeDataRequestStatus.type,
				this.productByIdDataRequestStatus.type,
				this.saveProductsRequestStatus.type,
			];
			return status.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getProductTypes: 'getProductTypes',
			getProductById: 'getProductById',
			saveProduct: 'saveProduct',
		}),
		load(): void {
			this.getProductTypes();
		},
		reload(): void {
			this.load();
		},
		submit(): void {
			const payload = createPayload(this.form, this.isEdit, this.currentId);
			this.saveProduct({
				type: this.isEdit ? EDIT : NEW,
				payload,
			});
		},
		updatedForm(): void {
			this.form = mapToDataForm(this.productByIdData.content);
		},
		updatedHeader(): void {
			this.$emit('onUpdateHeaderForm', {
				title: this.productByIdData.content.name,
				listIcons: [this.productByIdData.content.id, this.productByIdData.content.type],
			});
		},
		updatedDateFooter(): void {
			const updatedDataFooterForm = format(this.productByIdData.meta);
			this.$emit('onUpdateFooterFormData', updatedDataFooterForm);
		},
		createMessage(type: MessageTypes): string {
			const message = {
				ERROR: this.isEdit
					? 'Erro ao cadastrar o produto.'
					: 'Erro ao atualizar o produto.',
				SUCCESS: this.isEdit
					? `Dados do Produto atualizado com sucesso. Deseja continuar editando?`
					: `Produto cadastrado com sucesso. Deseja continuar para a edição?`,
			};

			return message[type];
		},
		redirectToHome(): void {
			this.$router.push({
				path: `/admin/cadastros/produtos`,
			});
		},
		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/produtos/${this.postCreatedProductId}/editar?path=dados`,
			});

			this.$router.go(0);
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.redirectToHome();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.reload();
				})
				.catch(() => {
					this.redirectToHome();
				});
		},

		updatePostCreatedProductId() {
			this.postCreatedProductId = this.productPostCreatedId.id;
		},
	},
	watch: {
		valid(newValue: boolean): void {
			this.$emit('onDisabledButtonFooter', newValue);
		},
		productTypeDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataSelect = this.productTypeData;
				if (this.isEdit) {
					this.getProductById({ id: this.currentId });
				}
			}
		},
		productByIdDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedForm();
				this.updatedHeader();
				this.updatedDateFooter();
			}
		},
		saveProductsRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.isEdit) {
					this.editDialog(this.createMessage('SUCCESS'));
					return;
				}
				this.updatePostCreatedProductId();
				this.createDialog(this.createMessage('SUCCESS'));
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessage('ERROR'));
			}
		},
	},
});
</script>
