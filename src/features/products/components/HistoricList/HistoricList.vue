<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<HistoricCard
					:data="item"
					@handleDetails="handleDetails"
					@handleDownload="handleDownload"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import HistoricCard from '../HistoricCard';

export default defineComponent({
	components: {
		HistoricCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		handleDetails(data): void {
			this.$emit('handleDetails', data);
		},
		handleDownload(data): void {
			this.$emit('handleDownload', data);
		},
	},
});
</script>
