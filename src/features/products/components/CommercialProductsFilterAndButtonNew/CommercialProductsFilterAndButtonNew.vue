<template>
	<farm-box>
		<farm-row justify="space-between">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Produto Comercial"
					:showFilters="showFilters"
					@onClick="onShowFilters"
					@onInputChange="onInputChangeFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					class="mt-8"
					customIcon="plus"
					title="Associar Produto Comercial"
					:icon="true"
					@click="onRedirectToAssociate"
				>
					Associar Produto Comercial
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<farm-row justify="start">
			<farm-col cols="12">
				<collapse-transition :duration="300">
					<commercial-product-filter-form
						v-show="showFilters"
						@onFilterClicked="onFilterClicked"
						@onFilter="onFilter"
					/>
				</collapse-transition>
		</farm-col>
		</farm-row>
		<farm-row justify="end">
			<farm-col cols="12" md="3">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortItems"
					@change="onChangeSort"
				/>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { sort as sortItems } from './configurations';
import CommercialProductFilterForm from '../CommercialProductFilterForm';

export default defineComponent({
	name: 'commercial-products-filter-and-button-new',
	components: {
		CommercialProductFilterForm
	},
	setup(_, { emit }) {
		const showFilters = ref(false);
		const sortModel = ref('associationStart_DESC');

		function onChangeSort(value): void {
			emit('onSortSelect', value);
		}

		function onShowFilters(value): void {
			emit('onClickMainFilter', value);
			showFilters.value = !showFilters.value;
		}

		function onInputChangeFilter(value): void {
			emit('onInputChangeMainFilter', value);
		}

		function onFilterClicked(value): void {
			emit('onFilterClicked', value);
		}

		function onFilter(value): void {
			emit('onFilter', value);
		}

		function onRedirectToAssociate(): void {
			emit('onRedirectToAssociate');
		}

		return {
			showFilters,
			sortItems,
			sortModel,
			onShowFilters,
			onInputChangeFilter,
			onChangeSort,
			onFilterClicked,
			onFilter,
			onRedirectToAssociate,
		};
	},
});
</script>
