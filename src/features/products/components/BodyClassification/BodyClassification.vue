<template>
	<farm-container>
		<HeaderForm withLine v-if="!isError" :data="dataHeaderForm" />
		<TitlePageForm
			v-if="!isError"
			value="Classificação"
			noPipe
			:label="!isEdit ? 'Adicionar' : 'Editar'"
		/>
		<ClassificationForm
			v-if="!isError"
			:form="form"
			:isEdit="isEdit"
			:data="infoClassification"
			:errorForm="errorForm"
			@onUpdatedStatusForm="onUpdatedStatusForm"
			@onDisabledButtonFooter="onDisabledButtonFooter"
		/>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="false"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notificationWrapper } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT } from '@/constants';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import TitlePageForm from '@/components/TitlePageForm';

import ClassificationForm from '../ClassificationForm';
import { headerPage } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		HeaderForm,
		FooterForm,
		ClassificationForm,
		TitlePageForm,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: true,
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			labelButton: 'Cadastrar',
			form: {
				name: '',
			},
			infoClassification: {},
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			errorForm: true,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			classificationData: 'classificationData',
			classificationDataById: 'classificationDataById',
			classificationDataByIdRequestStatus: 'classificationDataByIdRequestStatus',
			classificationDataRequestStatus: 'classificationDataRequestStatus',
			saveClassificationRequestStatus: 'saveClassificationRequestStatus',
		}),
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentId(): number {
			return this.$route.params.id;
		},
		currentIdClassification(): number {
			return this.$route.params.idClassification;
		},
		isLoading(): boolean {
			return (
				this.classificationDataRequestStatus === RequestStatusEnum.START ||
				this.saveClassificationRequestStatus === RequestStatusEnum.START ||
				this.classificationDataByIdRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return (
				this.classificationDataRequestStatus.type === RequestStatusEnum.ERROR ||
				this.classificationDataByIdRequestStatus.type === RequestStatusEnum.ERROR
			);
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.loadEditClassification();
			return;
		}
		this.loadNewClassification();
	},
	methods: {
		...mapActions('cadastros', {
			getProductClassification: 'getProductClassification',
			saveProductClassification: 'saveProductClassification',
			getProductClassificationById: 'getProductClassificationById',
		}),
		loadNewClassification(): void {
			this.getProductClassification({
				filters: { ...this.filters, ...this.hasSort },
				idProduct: this.currentId,
			});
		},
		loadEditClassification(): void {
			this.labelButton = 'Salvar';
			this.getProductClassificationById({
				filters: { ...this.filters, ...this.hasSort },
				idProduct: this.currentId,
				idClassification: this.currentIdClassification,
			});
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/produtos/${this.currentId}/editar?path=classificacao`,
			});
		},
		createPayload() {
			return {
				name: this.form.name.toUpperCase(),
			};
		},
		onSave(): void {
			const payload = this.createPayload();
			this.saveProductClassification({
				type: this.isEdit ? 'edit' : 'new',
				payload,
				idProduct: this.currentId,
				idClassification: this.currentIdClassification || 0,
			});
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		updatedHeader(data): void {
			this.dataHeaderForm = parseDataHeader(data, headerPage);
		},
		updatedForm(data): void {
			this.form = {
				name: data.name,
			};
		},
		updatedInfoClassification(data): void {
			this.infoClassification = {
				...data,
				idClassification: this.currentIdClassification || 0,
			};
		},
		createMessage(type): string {
			const actionSucess = this.isEdit ? 'atualizada' : 'cadastrada';
			const actionError = this.isEdit ? 'atualizadar' : 'cadastrar';
			const message = {
				ERROR: `${actionError} uma Classificação`,
				SUCCESS: `Classificação ${actionSucess}`,
			};
			return message[type];
		},
		redirectToHome(): void {
			setTimeout(() => {
				this.onCancel();
			}, 1500);
		},
		onUpdatedStatusForm(data): void {
			this.errorForm = data;
		},
	},
	watch: {
		classificationDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader({
					title: this.classificationData.header.name,
					listIcons: [
						this.classificationData.header.id,
						this.classificationData.header.type,
					],
				});
			}
		},
		saveClassificationRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notificationWrapper(
					newValue,
					this.createMessage(RequestStatusEnum.SUCCESS),
					this.createMessage(RequestStatusEnum.ERROR)
				);
				this.redirectToHome();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				this.onUpdatedStatusForm('Nome de classificação existente.');
			}
		},
		classificationDataByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader({
					title: this.classificationDataById.content.productName,
					listIcons: [
						this.classificationDataById.content.id,
						this.classificationDataById.content.type,
					],
				});
				this.updatedForm(this.classificationDataById.content);
				this.updatedInfoClassification(this.classificationDataById.content);
			}
		},
	},
});
</script>
