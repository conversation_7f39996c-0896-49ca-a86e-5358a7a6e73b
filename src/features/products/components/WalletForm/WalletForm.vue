<template>
	<farm-form v-model="valid">
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label for="input-product-wallet-name" required> Nome </farm-label>
				<farm-textfield-v2
					uppercase
					v-model="form.name"
					id="input-product-wallet-name"
					:rules="[rules.required, errorForm]"
					@change="changeInput"
				/>
			</farm-col>
			<farm-col cols="12" md="2" v-if="isEdit">
				<farm-idcaption
					class="margem-top-idCaption"
					icon="account-multiple-outline"
					copyText=""
					:link="true"
					@onLinkClick="onOpenModal"
				>
					<template v-slot:title> Sacados </template>
					<template v-slot:subtitle>{{ infoWallet.draweeCount }}</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12" md="2" v-if="isEdit">
				<farm-idcaption class="margem-top-idCaption" icon="calendar" copyText="">
					<template v-slot:title> Data de Criação </template>
					<template v-slot:subtitle>
						{{ defaultDateFormat(infoWallet.createdAt) }}
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12" md="3" v-if="isEdit">
				<farm-idcaption class="margem-top-idCaption" icon="update" copyText="">
					<template v-slot:title> Última Atualização </template>
					<template v-slot:subtitle>
						{{ infoWallet.updatedAt ? defaultDateFormat(infoWallet.updatedAt) : 'N/A' }}
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<ModalWalletDrawee
			v-if="showModalWalletDrawee"
			v-model="showModalWalletDrawee"
			:idWallet="infoWallet.id"
			@onClose="onCloseModal"
		/>
	</farm-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import ModalWalletDrawee from '../ModalWalletDrawee';

export default defineComponent({
	components: {
		ModalWalletDrawee,
	},
	props: {
		form: {
			type: Object,
		},
		isEdit: {
			type: Boolean,
		},
		infoWallet: {
			type: Object,
		},
		errorForm: {
			type: [Boolean, String],
		},
	},

	data() {
		return {
			valid: true,
			defaultDateFormat,
			showModalWalletDrawee: false,
			dataName: '',
			rules: {
				required: value => !!value || 'Campo obrigatório',
			},
		};
	},
	methods: {
		onCloseModal() {
			this.showModalWalletDrawee = false;
		},
		onOpenModal() {
			this.showModalWalletDrawee = true;
		},
		changeInput() {
			if (this.errorForm !== true) {
				this.$emit('onUpdatedStatusForm', true);
			}
		},
	},
	watch: {
		valid(newValue: boolean): void {
			this.$emit('onDisabledButtonFooter', newValue);
		},
	},
});
</script>
<style>
.margem-top-idCaption {
	margin-top: 21px;
}
</style>
