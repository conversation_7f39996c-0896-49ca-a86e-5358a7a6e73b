<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12"  md="6" v-for="item in data" :key="item.id">
				<OriginatorsAssignorsCard :data="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-else>
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import OriginatorsAssignorsCard from '../OriginatorsAssignorsCard';

export default defineComponent({
	components: {
		OriginatorsAssignorsCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
});
</script>
