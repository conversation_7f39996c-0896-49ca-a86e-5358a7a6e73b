<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<WalletCard
					:data="item"
					@onClickEdit="onClickEdit"
					@onClickRemove="onClickRemove"
					@openModalDrawee="openModalDrawee"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente adicionar uma carteira." />
			</farm-box>
		</farm-row>
		<ModalWalletDrawee
			v-if="showModalWalletDrawee"
			v-model="showModalWalletDrawee"
			:idWallet="currentIdWallet"
			@onClose="onCloseModal"
		/>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import WalletCard from '../WalletCard';
import ModalWalletDrawee from '../ModalWalletDrawee';

export default defineComponent({
	components: {
		WalletCard,
		ModalWalletDrawee,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			showModalWalletDrawee: false,
			currentIdWallet: 0,
		};
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		onClickEdit(item): void {
			this.$emit('handleClickItemEdit', item);
		},
		onClickRemove(item): void {
			this.$emit('handleClickItemRemove', item);
		},
		openModalDrawee(item) {
			this.currentIdWallet = item.id;
			this.showModalWalletDrawee = true;
		},
		onCloseModal() {
			this.showModalWalletDrawee = false;
		},
	},
});
</script>
