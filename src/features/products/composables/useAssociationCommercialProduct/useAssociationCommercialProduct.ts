import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';
import { getCurrentInstance } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { associationCommercialProduct as associationCommercialProductService } from '@/features/products/services';

type UseAssociationCommercialProduct = {
	isLoadingAssociationCommercialProduct: ComputedRef<boolean>;
	isErrorAssociationCommercialProduct: ComputedRef<boolean>;
	associationCommercialProduct: Function;
};

export function useAssociationCommercialProduct(): UseAssociationCommercialProduct {

	let callFunc: Function | null = null;
	let payloadCache = null;
	const internalInstance = getCurrentInstance().proxy;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => associationCommercialProductService(params),
		onSuccess: () => {
			if (callFunc !== null) {
				callFunc(payloadCache, RequestStatusEnum.SUCCESS);
			}
		},
		onError: (error) => {
			const isNotFound = (error as { message: any }).message.includes('404');
			if (isNotFound) {
				createNotificationErrorNotFound();
				return;
			}
			createNotificationError();
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc();
				}, 2500);
			}
		},
	});

	function createNotificationError(): void {
		const message = payloadCache.isFinish ? `finalizar a associação do` : `associar o`;
		notification(
			RequestStatusEnum.ERROR,
			`Não foi possível ${message} Produto Comercial. Por favor tente mais tarde.`
		);
	}

	function createNotificationErrorNotFound(): void {
		const message = payloadCache.isFinish ? `finalizar a associação` : `associar novamente`;

		const titleAndBody = {
			title: 'Produto Comercial Inativo',
			body: `Não foi possível ${message}, <br/> Produto Comercial inativo.`,

		};
		const configurtations = {
			html: true,
			okText: 'Fechar',

		};
		internalInstance['$dialog'].alert(titleAndBody, configurtations)
			.then(() => { })
			.catch(() => { });
	}

	const isLoadingAssociationCommercialProduct = computed(() => {
		return isLoading.value;
	});

	const isErrorAssociationCommercialProduct = computed(() => {
		return isError.value;
	});

	function associationCommercialProduct(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
		payloadCache = {
			name: payload.name,
			isFinish: payload.payload.enabled === 0
		};
	}

	return {
		isLoadingAssociationCommercialProduct,
		isErrorAssociationCommercialProduct,
		associationCommercialProduct,
	};
}
