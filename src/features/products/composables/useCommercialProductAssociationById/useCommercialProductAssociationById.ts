import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { getCommercialProductAssociation as getCommercialProductAssociationService } from '@/features/products/services';
import { builderCommercialProductAssociationById } from '../../helpers/builderCommercialProductAssociationById';
type UseCommercialProductById = {
	dataAssociation: Ref<any>;
	getAssociationtById: Function;
	isShowFields: Ref<boolean>;
	dataAssociationIsLoading: ComputedRef<boolean>;
	dataAssociationIsError: ComputedRef<boolean>;
	dataAssociationIsSuccess: ComputedRef<boolean>;
};

export function useCommercialProductAssociationById(): UseCommercialProductById {
	const dataAssociation = ref(null);
	const isShowFields = ref(false);

	let callFunc: Function | null = null;

	const { isLoading, isError, isSuccess, mutate } = useMutation({
		mutationFn: (params) => getCommercialProductAssociationService(params),
		onSuccess: (response) => {
			const data = builderCommercialProductAssociationById(response);
			dataAssociation.value = data;
			isShowFields.value = true;
			if (callFunc) callFunc(false, null);
		},
		onError: () => {
			dataAssociation.value = null;
			if (callFunc) callFunc(true, null);
		},
	});

	const dataAssociationIsLoading = computed(() => {
		return isLoading.value;
	});

	const dataAssociationIsError = computed(() => {
		return isError.value;
	});

	const dataAssociationIsSuccess = computed(() => {
		return isSuccess.value;
	});

	function getAssociationtById(data, callback: Function) {
		isShowFields.value = false;
		mutate(data);
		callFunc = callback;
	}

	return {
		dataAssociation,
		dataAssociationIsLoading,
		dataAssociationIsError,
		isShowFields,
		getAssociationtById,
		dataAssociationIsSuccess,
	};
}
