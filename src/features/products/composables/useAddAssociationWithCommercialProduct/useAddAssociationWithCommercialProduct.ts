import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { createAssociationWithCommercialProduct as createAssociationWithCommercialProductService } from '../../services';

type UseAddAssociationWithCommercialProduct = {
	isLoadingAddAssociation: ComputedRef<boolean>;
	isErrorAddAssociation: ComputedRef<boolean>;
	createAssociationWithCommercialProduct: Function;
};

export function useAddAssociationWithCommercialProduct(): UseAddAssociationWithCommercialProduct {

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => createAssociationWithCommercialProductService(params),
		onSuccess: () => {
			createNotification();
			if (callFunc !== null) {
				callFunc(RequestStatusEnum.SUCCESS);
			}
		},
		onError: () => {
			createNotificationError();
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc(RequestStatusEnum.ERROR);
				}, 1500);
			}
		},
	});

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			'Não foi possivel associar o Produto Comercial. Por favor tente mais tarde.'
		);
	}

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, 'Produto Comercial associado com sucesso!');
	}

	const isLoadingAddAssociation = computed(() => {
		return isLoading.value;
	});

	const isErrorAddAssociation = computed(() => {
		return isError.value;
	});

	function createAssociationWithCommercialProduct(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
	}

	return {
		createAssociationWithCommercialProduct,
		isErrorAddAssociation,
		isLoadingAddAssociation,
	};
}
