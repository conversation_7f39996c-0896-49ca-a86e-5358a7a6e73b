import { useMutation } from "@tanstack/vue-query";
import type { ComputedRef, Ref } from 'vue/types';
import { getCommercialProductModality as getCommercialProductModalityService } from '@/features/products/services';

import { computed, ref } from "vue";
import { builderComercialProductModality } from "../../helpers/builderComercialProductModality";

type CommercialProductModality = {
	commercialProductModality: Ref<Array<{ id: number, name: string }>>;
	isLoadingCommercialProductModality: ComputedRef<boolean>;
	isErrorCommercialProductModality: ComputedRef<boolean>;
	getCommercialProductModality: Function
}

export function useCommercialProductModality(): CommercialProductModality {
	const commercialProductModality: Ref<Array<{ id: number, name: string }>> = ref([]);
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getCommercialProductModalityService(),
		onSuccess: (response) => {
			commercialProductModality.value = builderComercialProductModality(response);
		},
		onError: () => {
			commercialProductModality.value = null;
		}
	});

	const isLoadingCommercialProductModality = computed(() => isLoading.value);
	const isErrorCommercialProductModality = computed(() => isError.value);

	function getCommercialProductModality(data, callback: Function) {
		mutate(data);
		callFunc = callback;
	}

	return {
		commercialProductModality,
		isLoadingCommercialProductModality,
		isErrorCommercialProductModality,
		getCommercialProductModality
	};
}
