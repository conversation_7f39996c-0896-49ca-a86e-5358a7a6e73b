import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { getCommercialProductsById as getCommercialProductByIdService } from '@/features/products/services';
import { builderCommercialProductById } from '@/features/products/helpers/builderCommercialProductById';
import { CommercialProduct } from '../../types';
import { AxiosResponse } from 'axios';

type UseCommercialProductById = {
	commercialProduct: Ref<CommercialProduct[]>;
	isLoadingCommercialProductById: ComputedRef<boolean>;
	isErrorCommercialProductById: ComputedRef<boolean>;
	getCommercialProductById: Function;
	commercialProductByIdPagination: Ref<any>;
};

export function useCommercialProductById(): UseCommercialProductById {
	const commercialProduct: Ref<CommercialProduct[]> = ref([]);
	const commercialProductByIdPagination = ref({});

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getCommercialProductByIdService(params),
		onSuccess: response => {
			const { content, pagination } = builderCommercialProductById(
				response as AxiosResponse<any>
			);
			commercialProduct.value = content;
			commercialProductByIdPagination.value = pagination;
			if (callFunc) callFunc(false, null);
		},
		onError: () => {
			commercialProduct.value = null;
			if (callFunc) callFunc(true, null);
		},
	});

	const isLoadingCommercialProductById = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductById = computed(() => {
		return isError.value;
	});

	function getCommercialProductById(data, callback: Function) {
		mutate(data);
		callFunc = callback;
	}

	return {
		commercialProduct,
		isLoadingCommercialProductById,
		isErrorCommercialProductById,
		commercialProductByIdPagination,
		getCommercialProductById,
	};
}
