<template>
	<farm-container>
		<farm-alertbox icon="alert-circle-outline">
			<farm-bodytext class="mb-0" type="1" color="primary" bold variation="regular">
				Critérios de Elegibilidade:
				<farm-typography tag="span" weight="400" color="primary">
					Veja se você está seguindo todos os requisitos para a elegibilidade
				</farm-typography>
			</farm-bodytext>

			<farm-btn
				class="farm-btn--responsive"
				title="Ver Critérios"
				@click="dialogCriteria = true"
			>
				<farm-icon>pound</farm-icon>
				Ver Critérios
			</farm-btn>
		</farm-alertbox>

		<farm-row extra-decrease class="mt-4">
			<farm-box v-if="parametersByFinancialVehicle">
				<v-data-table
					id="table-cadastros-parameters-list"
					hide-default-footer
					:items="parametersByFinancialVehicle"
					:headers="headers"
					:server-items-length="parametersByFinancialVehicle.length"
				>
					<template slot="no-data">
						<farm-emptywrapper />
					</template>

					<template v-slot:[`item.data`]="{ item }">
						{{ defaultDateFormat(item.data) }}
					</template>

					<template v-slot:footer>
						<farm-datatable-paginator
							class="mt-6 mb-n6"
							:page="currentPage"
							:totalPages="parametersByFinancialVehicleTotalPages"
							@onChangePage="onChangePage"
							@onChangeLimitPerPage="onChangeLimitPerPage"
						/>
					</template>
				</v-data-table>
				<ModalCriterion
					:dialogCriteria="dialogCriteria"
					:items="criteriaByFinancialVehicleList"
					@onClose="dialogCriteria = false"
				/>
				<farm-loader mode="overlay" v-if="isLoading" />
			</farm-box>
		</farm-row>
	</farm-container>
</template>
<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import {
	pageable,
	defaultDateFormat,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';

import { limit as limitMask } from '@/helpers/masks';

import { headers } from '../../configurations/headers';
import ModalCriterion from '../ModalCriterion';

export default defineComponent({
	components: {
		ModalCriterion,
	},
	mixins: [pageable],
	data() {
		return {
			filter: false,
			togglePopover: false,
			filters: {
				page: 0,
				limit: 10,
			},
			dialogCriteria: false,
			dialogImport: false,
			headers,
			lastSearchFilters: {},
			defaultDateFormat,
			limitMask,
			currentItem: null,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			parametersByFinancialVehicle: 'parametersByFinancialVehicle',
			parametersByFinancialVehicleTotalPages: 'parametersByFinancialVehicleTotalPages',
			criteriaByFinancialVehicleList: 'criteriaByFinancialVehicleList',
			parametersByFinancialVehicleRequestStatus: 'parametersByFinancialVehicleRequestStatus',
			criteriaFinancialVehicleRequestStatus: 'criteriaFinancialVehicleRequestStatus',
			selectedProduct: 'selectedProduct',
		}),
		isLoading() {
			return (
				this.parametersByFinancialVehicleRequestStatus === RequestStatusEnum.START ||
				this.criteriaFinancialVehicleRequestStatus === RequestStatusEnum.START
			);
		},
	},
	methods: {
		...mapActions('cadastros', {
			fetchParametersByFinancialVehicle: 'fetchParametersByFinancialVehicle',
			fetchCriteriaByFinancialVehicle: 'fetchCriteriaByFinancialVehicle',
		}),
		formatDate(date) {
			return date ? defaultDateFormat(date) : '';
		},
		doSearch() {
			this.lastSearchFilters = { ...this.filters };
			this.fetchParametersByFinancialVehicle({
				idProduct: this.selectedProduct.id,
				filters: { ...this.filters },
			});
		},
		closeModal() {
			this.currentItem = null;
		},
	},
	mounted() {
		this.fetchCriteriaByFinancialVehicle();

		this.doSearch();
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-cadastros-parameters-list', 1, (0));
</style>
<style lang="scss" scoped>
@import './Body';
</style>
