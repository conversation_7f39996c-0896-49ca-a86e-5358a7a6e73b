<template>
	<farm-modal v-model="dialog" :offsetTop="48" :offsetBottom="0">
		<template v-slot:header>
			<farm-dialog-header :title="title" @onClose="$emit('onClose')" />
		</template>
		<template v-slot:content>
			<farm-row extra-decrease>
				<farm-box>
					<section id="tables-container">
						<div :class="!firstPage ? 'table-hide' : ''">
							<farm-form ref="form" v-model="valid" class="mt-3 mb-0">
								<farm-row class="pt-3 pb-5 px-9">
									<farm-bodytext type="2" variation="medium">
										Critérios AR preparado para desembolso - 17, 19, 20, 26, 27,
										30, 32
									</farm-bodytext>
								</farm-row>
								<farm-line noSpacing />
								<farm-row
									justify="space-between"
									align="center"
									no-gutters
									class="mt-4 mx-3"
								>
									<farm-col cols="12" md="6">
										<farm-form-mainfilter
											label="Buscar critério"
											:hasExtraFilters="false"
											@onInputChange="filterInputChangedCustom"
										/>
									</farm-col>
									<farm-col
										cols="12"
										sm="4"
										class="pr-0 pr-md-5 mb-4"
										align="right"
									>
										<farm-btn
											class="mt-6"
											title="Ver Critérios"
											@click="switchPage()"
										>
											<farm-icon>pound</farm-icon>
											Ver Critério 29
										</farm-btn>
									</farm-col>
								</farm-row>
							</farm-form>

							<v-data-table
								:items="criteria"
								hide-default-footer
								class="elevation-0 pb-4 mt-0"
								:headers="headers"
								:options.sync="pagination"
								id="table-cadastros-providers-list"
							>
								<template slot="no-data">
									<DataTableEmptyWrapper subtitle="" />
								</template>

								<template v-slot:[`item.id`]="{ item }">
									<farm-bodytext
										type="2"
										class="table-id"
										variation="regular"
										color="secondary"
										bold
									>
										#{{ item.id }}
									</farm-bodytext>
								</template>

								<template v-slot:[`item.errorMessage`]="{ item }">
									<farm-bodytext
										type="2"
										class="table-errorMessage"
										variation="regular"
									>
										#{{ item.menssageErro }}
									</farm-bodytext>
								</template>

								<template v-slot:footer>
									<DataTablePaginator
										class="mt-6"
										:hidePerPageOptions="true"
										@onChangePage="newPage => (pagination.page = newPage)"
										:page="pagination.page"
										:totalPages="pagination.pages"
									/>
								</template>
							</v-data-table>
						</div>

						<div>
							<farm-row class="mt-7" no-gutters>
								<farm-col cols="12" class="py-4 px-12">
									<farm-alertbox color="info">
										<div>
											<farm-heading
												type="6"
												color="info"
												color-variation="darken"
												class="mb-2"
											>
												Doc críterio 29
											</farm-heading>
											<br />
											RELACIONAMENTO_MAX_ANO >> Inteiro >> Você informa o
											maior tempo de relacionamento possível entre os
											segmentos, nesse caso: 5 <br /><br />RELACIONAMENTO_SET
											>> String com sintaxe:
											<ul>
												<br />
												<li>
													<dl>
														<dt class="font-weight-bold">C</dt>
														<dd>
															CA onde C = critério e A = segmento (no
															caso com corteva)
														</dd>
														<dd>
															C1 onde C = critério e 1 = segmento (no
															caso com sygenta)
														</dd>
													</dl>
												</li>
												<br />
												<li>
													<dl>
														<dt class="font-weight-bold">R</dt>
														<dd>
															R1 onde R = relacionamento e 1 = um ano
															de relacionamento
														</dd>
														<dd>
															R2 onde R = relacionamento e 2 = dois
															anos de relacionamento
														</dd>
														<dd>
															R5 onde R = relacionamento e 3 = três
															anos de relacionamento
														</dd>
													</dl>
												</li>
												<br />
												<li>
													<dl>
														<dt class="font-weight-bold">P</dt>
														<dd>
															P2 onde P = percentual do PL e 2 = dois
															por cento do PL
														</dd>
														<dd>
															P5 onde P = percentual do PL e 5 = dois
															por cento do PL
														</dd>
													</dl>
												</li>
											</ul>
											<br />
											Separando por virgula o conjunto
											<b>CA:R1:P2</b> por exemplo:<br />
											<b>Sygenta:</b>

											C1:R1:P5,C2:R5:P5,C3:R2:P5,C4:R2:P5,C5:R2:P5
										</div>
									</farm-alertbox>
								</farm-col>
							</farm-row>
							<farm-row no-gutters>
								<farm-col cols="12">
									<DialogFooter
										confirmLabel="Voltar"
										:hasCancel="false"
										@onConfirm="switchPage"
									/>
								</farm-col>
							</farm-row>
						</div>
					</section>
				</farm-box>
			</farm-row>
		</template>
	</farm-modal>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { criterionHeader as headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		items: Array,
		dialogCriteria: Boolean,
	},
	data() {
		return {
			valid: false,
			headers,
			filter: '',
			criteria: null,
			dialog: false,
			pagination: {
				page: 1,
				itemsPerPage: 10,
			},
			title: 'Critérios de Elegibilidade',
			firstPage: true,
		};
	},
	watch: {
		dialogCriteria(v) {
			this.dialog = v;
			this.criteria = this.items;
			this.pagination.pages = Math.ceil(this.items.length / this.pagination.itemsPerPage);
		},
	},
	methods: {
		switchPage() {
			this.firstPage = !this.firstPage;
		},
		filterInputChangedCustom(value: string) {
			if (value.length >= 3 || value === '') {
				this.criteria = this.items.filter(el => el.menssageErro.includes(value));
			}
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-cadastros-providers-list', 1, (0));
</style>

<style lang="scss" scoped>
@import './ModalCriterion.scss';
</style>
