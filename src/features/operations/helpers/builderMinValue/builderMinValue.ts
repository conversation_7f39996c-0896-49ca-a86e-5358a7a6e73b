import { format } from '@/helpers/formatUpdateUser';

function getKey(value, index): string {
	return `${value.name}-${value.id}-${index}`;
}

function checkStatus(value: number): boolean {
	return value === 1 ? true : false;
}

function getStatusName(value: number): string {
	return value === 1 ? 'Ativo' : 'Inativo';
}

function checkModalities(data, keyParent) {
	const modalitieEnableds = data.filter((item) => {
		return item.enabled === 1;
	});
	if(modalitieEnableds.length > 0) {
		return getModalities(modalitieEnableds, keyParent);
	}
	return [];
}

function getModalities(data, keyParent) {
	const modalities = data.map((item) => {
		return {
			key: `${keyParent}-${item.name}-${item.id}`,
			name: item.name,
			status: checkStatus(item.enabled)
		};
	});
	return modalities;
}

function getFields(data, keyParent){
	const newData = data.map((item, index) => {
		return {
			key: `${keyParent}-${item.name}-${index}`,
			label: item.name,
			id: item.id,
			minimumValue: item.minimumValue,
			value: item.minimumValue || 0,
			disabled: !checkStatus(item.enabled)
		};
	});
	return newData;
}

function checkFormStatus(data, keyParent) {
	const newData = data.map((item, index)=>{
		return {
			key: `${keyParent}-${item.name}-${index}`,
			id: item.id,
			title: item.name,
			status: checkStatus(item.enabled),
			fields: getFields(item.settlementTypes, keyParent)
		};
	});
	return newData;
}

export function builderMinValue(response) {
	const { content, page, size, totalItems, totalPages, totalElements, meta } = response.data;
	const newData = content.map((item, index) => {
		return {
			key: getKey(item, index),
			id: item.id,
			name: item.name,
			modalities: checkModalities(item.modalities, getKey(item, index)),
			status: checkStatus(item.enabled),
			statusName: getStatusName(item.enabled),
			forms: checkFormStatus(item.modalities, getKey(item, index))
		};
	});
	return {
		content: newData,
		pagination: {
			pageNumber: page,
			pageSize: size,
			sort: null,
			totalElements: totalItems,
			totalPages: totalPages,
		},
		totals: parseInt(totalElements),
		meta: format({
			createdAt: null,
			createdBy: null,
			updatedAt: meta.updatedAt || null,
			updatedBy: meta.updatedBy || null
		})
	};
}
