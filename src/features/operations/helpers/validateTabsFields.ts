export function areAllFieldsValid(modalitiesData) {
  const fieldsToValidate = [
    modalitiesData.cessao,
    modalitiesData.creditoPonte,
    modalitiesData.originacao,
  ];

  return fieldsToValidate.every((modality) => {
    if (!modality) return false;
    const { day, hour, daysPlus } = modality;

    const isCessaoValid =
      modality === modalitiesData.cessao && !isNaN(day) && !isNaN(hour);
    const isCreditoPonteValid =
      modality === modalitiesData.creditoPonte &&
      !isNaN(day) &&
      !isNaN(daysPlus) &&
      !isNaN(hour);
    const isOriginationValid =
      modality === modalitiesData.originacao &&
      !isNaN(day) &&
      !isNaN(daysPlus) &&
      !isNaN(hour);

    return isCessaoValid || isCreditoPonteValid || isOriginationValid;
  });
}
