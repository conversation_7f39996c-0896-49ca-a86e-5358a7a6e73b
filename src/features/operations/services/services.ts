import { queryString, environment } from '@farm-investimentos/front-mfe-libs-ts';


import registerV3Service from '@/configurations/services/registerV3';

const client = registerV3Service;
const domainJava = environment.apiSuperCessaoV2;

export const getCommercialProductMinValues = (data) => {
	const filters = queryString(data, {});
	const url = `/api/v1/commercial-product/minimum-values?${filters}`;
	return client.get(url);
};

export const updatedMinValues = (data) => {
	const url = `/api/v1/commercial-product/minimum-values`;
	return client.put(url, data);
};

export const getDateAndHour = () => {
	const url = `/parameters/configuration/v2/deadline`;
	return client.get(`${domainJava}${url}`);
};

export const editDateAndHour = ( payload ) => {
	const url = `/parameters/configuration/v2/deadline`;
	return client.patch(`${domainJava}${url}`, payload);
};
