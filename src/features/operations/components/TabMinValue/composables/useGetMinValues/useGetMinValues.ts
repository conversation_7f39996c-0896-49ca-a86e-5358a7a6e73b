

import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderMinValue } from '@/features/operations/helpers/builderMinValue';
import { getCommercialProductMinValues } from '@/features/operations/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseGetMinValues = {
	commercialProducts: Ref<Array<any>>;
	commercialProductsPagination: Ref<any>;
	isLoadingCommercialProducts: ComputedRef<boolean>;
	isErrorCommercialProducts: ComputedRef<boolean>;
	getCommercialProducts: Function;
};

export function useGetMinValues(): UseGetMinValues {
	const commercialProducts = ref([]);
	const commercialProductsPagination = ref({});

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getCommercialProductMinValues(params),
		onSuccess: (response) => {
			const { content, pagination, totals, meta } = builderMinValue(response);
			commercialProducts.value = content;
			commercialProductsPagination.value = pagination;
			if(callFunc) callFunc(false, {totals, meta});
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				commercialProducts.value = [];
				if(callFunc) callFunc(false);
				return;
			}
			commercialProducts.value = [];
			commercialProductsPagination.value = null;
			if(callFunc) callFunc(true, 0);
		},
	});

	const isLoadingCommercialProducts = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProducts = computed(() => {
		return isError.value;
	});

	function getCommercialProducts(filters, callback: Function) {
		mutate(filters);
		callFunc = callback;
	}

	return {
		commercialProducts,
		commercialProductsPagination,
		isLoadingCommercialProducts,
		isErrorCommercialProducts,
		getCommercialProducts,
	};
}
