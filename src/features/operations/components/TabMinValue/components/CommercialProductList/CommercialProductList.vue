<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" v-for="item in data" :key="item.key">
				<commercial-product-card :data="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import CommercialProductCard from '../CommercialProductCard';

export default defineComponent({
	name: 'commercial-product-list',
	components: {
		CommercialProductCard
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props) {
		const { data } = toRefs(props);

		const isDataEmpty = computed(() => data.value.length === 0);

		return {
			isDataEmpty
		};
	},
});
</script>
