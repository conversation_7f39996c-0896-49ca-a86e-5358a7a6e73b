<template>
	<farm-box>
		<farm-row class="mt-4 mb-4">
			<farm-col cols="12">
				<div class="d-flex align-center">
					<div class="mobility-card-content-img">
						<img
							src="@/assets/icons/currency_exchange.svg"
							alt="imagem referente a dinheiro"
						/>
					</div>
					<div>
						<farm-bodytext variation="bold" :type="1">
							Modalidades de {{data.title}}
						</farm-bodytext>
					</div>
				</div>
			</farm-col>
		</farm-row>
		<farm-row>
			<farm-col cols="12" md="4" sm="4" v-for="item in data.fields" :key="item.key">
				<farm-label for="form-status">
					{{ item.label }}
				</farm-label>
				<farm-textfield-v2
					v-model="item.value"
					:mask="currencyMask"
					:disabled="item.disabled"
					:rules="[required]"
					@change="(value) => onChange(value, data, item)"
				/>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import { currency as currencyMask } from '@/helpers/masks';
import { debounce } from '@/helpers/debounce';

import { useMinValueCache } from '../../composables/useMinValueCache';

export default defineComponent({
	name: 'mobility-card',
	props: {
		data: {
			type: Object,
			required: true,
		},
		commercialProductId:{
			type: Number,
			required: true
		}
	},
	setup(props) {
		const { data, commercialProductId } = toRefs(props);

		const { addValueMin } = useMinValueCache();

		function nextStep(valueMin, modalityId, typeId): void {
			const payload = {
				commercialProductId: commercialProductId.value,
				modalityId,
				typeId,
				valueMin
			};
			addValueMin(payload);
		}

		function onChange(value, modalidade, type): void {
			const { debouncedFunction } = debounce(()=> {
				return nextStep(value, modalidade.id, type.id);
			}, 300);
			debouncedFunction();
		}

		function required(value: string): boolean | string {
			if(value.length > 2){
				return true;
			}
			return 'Valor obrigatório';
		}

		return {
			data,
			currencyMask,
			onChange,
			required
		};
	}
});
</script>
<style lang="scss" scoped>
@import './ModalityCard';
</style>

