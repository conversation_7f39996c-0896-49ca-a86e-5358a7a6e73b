<template>
 <farm-row>
  <farm-col>
   <tabs-form
    :tabList="tabsList"
    :valueDefault="valueDefault"
    @onUpdateCurrentTab="onUpdateCurrentTab"
   />
   <tab-min-value v-if="isTabMinValue" />
   <!-- <some-component v-if="isTabDate" /> -->
   <tab-date v-if="isTabDate" />
  </farm-col>
 </farm-row>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref } from 'vue';
import SomeComponent from './SomeContent.vue';
import TabDate from '../TabDate/TabDate.vue';

import TabsForm from '@/components/TabsForm';
import { MIN_VALUE, TAB_DATE } from '@/constants';

import { tabs } from './configurations';
import TabMinValue from '../TabMinValue';

export default defineComponent({
 name: 'tabs-content',
 components: {
  TabsForm,
  TabDate,
  TabMinValue,
  SomeComponent,
 },
 setup() {
  const tabsList = ref([]);
  const currentTab = ref(MIN_VALUE);
  const valueDefault = ref(MIN_VALUE);

  const isTabMinValue = computed(() => currentTab.value === MIN_VALUE);
  const isTabDate = computed(() => currentTab.value === TAB_DATE);

  function onUpdateCurrentTab(value): void {
   currentTab.value = value;
  }

  function initValueTab(): void {
   tabsList.value = [...tabs];
  }

  onMounted(() => {
   initValueTab();
   onUpdateCurrentTab(MIN_VALUE);
  });

  return {
   isTabMinValue,
   isTabDate,
   tabsList,
   currentTab,
   valueDefault,
   onUpdateCurrentTab,
  };
 },
});
</script>