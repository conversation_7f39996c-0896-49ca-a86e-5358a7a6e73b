<template>
	<farm-form v-model="valid">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<farm-collapsible open title="Informações Gerais" icon="clipboard-text">
					<GeneralInfoFormPJ v-model="form.generalInfo" type="sacado" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12" v-if="!isLoading" class="mt-4">
				<farm-collapsible title="Faturamento" icon="currency-usd">
					<BillingForm v-model="form.billing" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12">
				<farm-collapsible title="Endereço" icon="map-marker" class="mt-4">
					<AddressForm v-model="form.address" isCompany />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12">
				<farm-collapsible title="Contato Principal" icon="contacts" class="mt-4">
					<ContactForm v-model="form.contact" :isFromFormPJ="true" />
				</farm-collapsible>
			</farm-col>

			<farm-col cols="12">
				<farm-collapsible title="Responsável" icon="account" class="my-4">
					<AccountableForm v-model="form.responsible" />
				</farm-collapsible>
			</farm-col>
		</farm-row>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:hiddenButton="!DATA"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	notification,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm from '@/components/FooterForm';
import GeneralInfoFormPJ, {
	generalInfoModelWithDrawee,
	GeneralInfoPJTypes,
} from '@/components/GeneralInfoFormPJ';
import AddressForm, { addressModel, AddressTypes } from '@/components/AddressForm';
import ContactForm, { ContactTypes, contactModel } from '@/components/ContactForm';
import BillingForm, { receivedValueModel, receivedValueTypes } from '@/components/BillingForm';
import AccountableForm, { AccountableTypes, accountableModel } from '@/components/AccountableForm';

import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';
import { checkOriginURL } from '@/helpers/checkOriginURL';
import { addMaskInput } from '@/helpers/addMaskInput';

import { PJ, EDIT, DATA } from '@/constants';
import storage from '@/helpers/storage';
import updatePJDraweeFormVariables from '@/helpers/builders/updatePJDraweeFormVariables';
import createPjDraweeFormPayload from '@/helpers/builders/createPJDraweeFormPayload';

export default defineComponent({
	components: {
		FooterForm,
		GeneralInfoFormPJ,
		AddressForm,
		ContactForm,
		BillingForm,
		AccountableForm,
	},
	props: {
		type: {
			type: String,
			required: true,
		},
	},
	data() {
		return {
			valid: null,
			disabledButtonFooter: false,
			dataFooterForm: {},
			form: {
				generalInfo: createObject<GeneralInfoPJTypes>(generalInfoModelWithDrawee),
				address: createObject<AddressTypes>(addressModel),
				contact: createObject<ContactTypes>(contactModel),
				billing: createObject<receivedValueTypes>(receivedValueModel),
				responsible: createObject<AccountableTypes>(accountableModel),
			},
			formType: PJ,
			labelButton: 'Cadastrar',
			DATA,
		};
	},
	mounted(): void {
		if (this.isEdit) {
			this.labelButton = 'Salvar';
			this.load();
			return;
		}
		const document = addMaskInput({
			value: this.currentDocument,
			mask: '##.###.###/####-##',
			quantMin: 14,
		});
		this.form.generalInfo.document = document;
		if (storage.get('branch')) {
			this.load();
		}
	},
	computed: {
		...mapGetters('cadastros', {
			draweeById: 'draweeById',
			draweeByIdRequestStatus: 'draweeByIdRequestStatus',
			saveDraweeRequestStatus: 'saveDraweeRequestStatus',
			draweeDataHeader: 'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeByIdRequestStatus,
				this.saveDraweeRequestStatus,
				this.draweeDataHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.draweeByIdRequestStatus.type,
				this.saveDraweeRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
		hasOriginURL(): string {
			return this.$route.query.origin || '';
		},
		currentTypeForm(): string {
			return this.$route.params.typeForm;
		},
	},
	methods: {
		...mapActions('cadastros', {
			saveDrawee: 'saveDrawee',
			getDraweeById: 'getDraweeById',
			getDraweeHeaders: 'getDraweeHeaders',
		}),
		load(): void {
			this.getDraweeById({
				type: PJ,
				document: this.currentDocument,
			});

			this.getDraweeHeaders({
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.load();
		},
		onCancel(): void {
			let path = checkOriginURL(this.hasOriginURL, `/admin/cadastros/sacados`);
			this.$router.push({
				path,
			});
		},
		onSave(): void {
			const payload = createPjDraweeFormPayload(this.form);
			if (this.type === 'new') delete payload.id;
			this.saveDrawee({ payload, type: this.type });
		},
		onUpdateDataUser(data): void {
			const formattedData = format(data.meta);
			this.dataFooterForm = formattedData;
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [data.id, 'Jurídica', [data.document, data.raiz]],
			});
		},

		createMessage(type: 'ERROR' | 'SUCCESS'): string {
			const message = {
				ERROR: this.isEdit ? 'Erro ao cadastrar o sacado.' : 'Erro ao atualizar o sacado.',
				SUCCESS: this.isEdit
					? `Dados do Sacado atualizado com sucesso. Deseja continuar editando?`
					: `Sacado cadastrado com sucesso. Deseja continuar para a edição?`,
			};

			return message[type];
		},
		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/sacados/pj/${this.currentDocument}/editar?path=dados`,
			});

			this.$router.go(0);
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.onCancel();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.reload();
				})
				.catch(() => {
					this.onCancel();
				});
		},
	},
	watch: {
		saveDraweeRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.isEdit) {
					this.editDialog(this.createMessage('SUCCESS'));
					return;
				}
				this.createDialog(this.createMessage('SUCCESS'));
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessage('ERROR'));
			}
		},
		draweeByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.form = {
					...updatePJDraweeFormVariables(this.draweeById.content),
				};
				this.onUpdateDataUser(this.draweeById);
			}
		},
		draweeDataHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.draweeDataHeader.content);
			}
		},
		valid(value): void {
			this.disabledButtonFooter = value;
		},
	},
});
</script>
