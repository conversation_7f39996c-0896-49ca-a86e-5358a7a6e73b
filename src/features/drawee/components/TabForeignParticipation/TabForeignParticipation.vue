<template>
	<farm-box>
		<PageHome domain="sacados" />
	</farm-box>
</template>

<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';

import PageHome from '@/modules/foreignParticipation/components/PageHome';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { PF } from '@/constants';

export default defineComponent({
	components: {
		PageHome
	},
	computed: {
		...mapGetters('cadastros', {
			draweeDataHeader:'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			return this.draweeDataHeaderRequestStatus === RequestStatusEnum.START;
		},
		isPF(): boolean {
			return this.$route.params.typeForm === PF.toLowerCase();
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted() {
		this.getDraweeHeaders({
			document: this.currentDocument
		});
	},
	methods: {
		...mapActions('cadastros', {
			getDraweeHeaders:'getDraweeHeaders',
		}),
		updatedHeader(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [
					data.id,
					data.type,
					[data.document, data.raiz],
				],
			});
			this.currentPeopleId = data.peopleId;
		},
	},
	watch: {
		draweeDataHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.draweeDataHeader.content);
			}
		},
	},
});
</script>
