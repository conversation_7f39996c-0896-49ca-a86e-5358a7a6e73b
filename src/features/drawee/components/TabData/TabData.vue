<template>
	<farm-box>
		<FormPF :type="type" v-if="isPF" @onUpdateHeaderForm="onUpdateHeaderForm" />
		<FormPJ :type="type" v-if="!isPF" @onUpdateHeaderForm="onUpdateHeaderForm" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { PF } from '@/constants';

import FormPF from '../FormPF';
import FormPJ from '../FormPJ';

export default defineComponent({
	components: {
		FormPF,
		FormPJ,
	},
	props: {
		type: {
			type: String,
			required: true,
		},
	},
	computed: {
		isPF(): boolean {
			return this.$route.params.typeForm === PF.toLowerCase();
		},
	},
	methods: {
		onUpdateHeaderForm(data): void {
			this.$emit('onUpdateHeaderForm', data);
		},
	},
});
</script>
