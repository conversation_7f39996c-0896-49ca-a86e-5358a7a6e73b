<template>
	<farm-box>
		<PageHome
			v-if="!isError"
			domain="sacados"
			:document="currentDocument"
			:peopleId="currentPeopleId"
			@updatedFooter="updatedFooter"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<FooterForm :hiddenButton="true" :data="dataFooterForm" @onCancel="onCancel" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { format } from '@/helpers/formatUpdateUser';
import { createObject } from '@/helpers/createObject';
import PageHome from '@/modules/corporateStructure/components/PageHome';

export default defineComponent({
   components: {
		PageHome,
		FooterForm
	},
	data() {
		return {
			currentPeopleId: 0,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
		};
	},
	computed: {
		...mapGetters('cadastros', {
			draweeDataHeader:'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeDataHeaderRequestStatus
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [this.draweeDataHeaderRequestStatus.type];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getDraweeHeaders:'getDraweeHeaders',
		}),
		doSearch(): void {
			this.getDraweeHeaders({
				document: this.currentDocument
			});
		},
		reload(): void {
			this.doSearch();
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/sacados`,
			});
		},
		onUpdateDocumentsDataUser(data): void {
			this.$emit('onUpdateFooterFormData', data);
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [
					data.id,
					'Jurídica',
					[data.document, data.raiz],
				],
			});
			this.currentPeopleId = data.id;
		},
		updatedFooter(data): void {
			const updatedData = format(data);
			this.dataFooterForm = updatedData;
		},
	},
	watch: {
		draweeDataHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.draweeDataHeader.content);
			}
		},
	},
});
</script>
