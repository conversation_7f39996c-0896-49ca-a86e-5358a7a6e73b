<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col
				cols="12"
				sm="12"
				md="6"
				v-for="(item, index) in data"
				:key="`Product-card-${item.id}-${index}`"
			>
				<ProductsCard
					:data="item"
					@handleEdit="handleEdit"
					@handleFinalizeAssociation="handleFinalizeAssociation"
					@handleReAssociateOption="handleReAssociateOption"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente associar um produto." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ProductsCard from '../ProductsCard';

export default defineComponent({
	components: {
		ProductsCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		handleFinalizeAssociation(data): void {
			this.$emit('handleFinalizeAssociation', data);
		},
		handleReAssociateOption(data): void {
			this.$emit('handleReAssociateOption', data);
		},

		handleEdit(data): void {
			this.$emit('handleEdit', data);
		},
	},
});
</script>
