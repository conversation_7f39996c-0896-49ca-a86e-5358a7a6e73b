<template>
	<farm-container>
		<HeaderForm v-if="isEdit" :data="dataHeaderForm" />
		<farm-row extra-decrease>
			<farm-tabs
				class="mt-n6 mb-6"
				ref="tabEl"
				:tabs="tabs"
				:allowUserChange="true"
				:showCounter="false"
				@update="updateTab"
			/>
		</farm-row>
		<TabData v-if="currentTab === DATA" :type="type" @onUpdateHeaderForm="onUpdateHeaderForm" />
		<TabDocuments v-if="currentTab === DOCUMENTS" @onUpdateHeaderForm="onUpdateHeaderForm" />
		<TabCorporateStructure
			v-if="currentTab === CORPORATE_STRUCTURE"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabForeignParticipation
			v-if="currentTab === FOREIGNPARTICIPATION"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabProducts v-if="currentTab === PRODUCTS" @onUpdateHeaderForm="onUpdateHeaderForm" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import {
	DATA,
	DOCUMENTS,
	EDIT,
	FOREIGNPARTICIPATION,
	PRODUCTS,
	CORPORATE_STRUCTURE,
	PF,
} from '@/constants';
import { TabTypes } from '@/types';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import TabData from '../TabData';
import TabDocuments from '../TabDocuments';
import TabProducts from '../TabProducts';
import TabForeignParticipation from '../TabForeignParticipation';
import TabCorporateStructure from '../TabCorporateStructure';

import { tabDefault, tabEdit, tabEditPJ } from '../../configurations/tabs';
import { headersPages } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		FooterForm,
		TabData,
		TabDocuments,
		TabProducts,
		HeaderForm,
		TabForeignParticipation,
		TabCorporateStructure,
	},
	props: {
		type: {
			type: String,
			required: true,
		},
	},
	data() {
		return {
			tabs: tabDefault,
			currentTab: DATA,
			DATA,
			DOCUMENTS,
			PRODUCTS,
			FOREIGNPARTICIPATION,
			CORPORATE_STRUCTURE,
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
		};
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	computed: {
		isEdit(): boolean {
			return this.type === EDIT;
		},
		isNotData(): boolean {
			return this.currentTab !== DATA;
		},
		currentTypeForm(): string {
			return this.$route.params.typeForm;
		},
	},
	methods: {
		updateTab(item: TabTypes): void {
			if (!item) {
				return;
			}
			if (item.path === this.$route.query.path) {
				return;
			}

			this.$router.replace({
				query: {
					path: item.path,
					origin: this.$route.query.origin,
				},
			});
			this.currentTab = item.path;
		},
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			const isPF = this.currentTypeForm.toUpperCase() === PF;
			this.tabs = isPF ? tabEdit : tabEditPJ;
			let path = this.$route.query.path;
			if (!path) {
				path = DATA;
				this.$router.replace({
					path: this.$route.path,
					query: { path },
				});
			}
			this.currentTab = path;
			const index = this.tabs.findIndex(tab => path === tab.path);
			if (this.$refs.tabEl && this.$refs.tabEl.toIndex) {
				this.$refs.tabEl.toIndex(index);
			}
		},
		onUpdateHeaderForm(data) {
			this.dataHeaderForm = parseDataHeader(data, headersPages);
		},
	},
});
</script>
