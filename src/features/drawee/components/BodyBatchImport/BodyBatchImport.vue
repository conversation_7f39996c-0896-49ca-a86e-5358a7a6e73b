<template>
	<farm-container>
		<TabsForm
			:tabList="tabs"
			:valueDefault="IMPORT"
			:isEdit="true"
			@onUpdateCurrentTab="onUpdateCurrentTab"
		/>
		<TabImport
			v-if="isTabImport"
			@onSubmit="onSubmit"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@changeRouter="changeRouter"
		/>
		<TabHistoric v-if="isTabHistoric" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="false"
			:hiddenButton="isTabHistoric"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import TabsForm, { TabsFormTypes } from '@/components/TabsForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { HISTORIC, IMPORT } from '@/constants';
import { tabsImport } from '@/configurations/tabs';

import TabImport from '../TabImport';
import TabHistoric from '../TabHistoric';

export default defineComponent({
	components: {
		TabsForm,
		TabImport,
		TabHistoric,
		FooterForm,
	},

	data() {
		return {
			disabledButtonFooter: null,
			tabs: tabsImport,
			currentTab: IMPORT,
			HISTORIC,
			IMPORT,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dispatchSubmit: null,
			labelButton: 'Importar',
		};
	},
	computed: {
		isTabHistoric(): boolean {
			return this.currentTab === HISTORIC;
		},
		isTabImport(): boolean {
			return this.currentTab === IMPORT;
		},
	},
	mounted(): void {
		this.tabs = [...tabsImport];
	},
	methods: {
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/sacados`,
			});
		},
		onUpdateCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		changeRouter(): void {
			this.currentTab = HISTORIC;
			this.$router.replace({ query: { path: 'historico' } });
			this.tabs = [...tabsImport];
		},
	},
});
</script>
