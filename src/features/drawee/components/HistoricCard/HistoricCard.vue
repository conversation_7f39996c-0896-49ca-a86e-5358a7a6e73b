<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="8">
					<CardTextBody
						label="Data do Envio"
						:value="formatDataDrawee(data.createdAt)"
						ellipsisValue
					/>
				</farm-col>
				<farm-col cols="4" align="right">
					<HistoricStatus :status="data.statusId" dense />
					<farm-btn icon @click="handleCardItem(data)" title="Ver detalhes">
						<farm-icon size="sm">open-in-new</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody label="Nome do Arquivo" :value="data.filename" ellipsisValue />
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Usuário" :value="data.createdBy" ellipsisValue />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { edit as editOption } from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import HistoricStatus from '@/components/HistoricStatus';
import {  formatDataDrawee } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		HistoricStatus,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	methods: {
		formatDataDrawee,
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption];
		},
		handleCardItem(data): void {
			this.$emit('handleCardItem', data);
		},
	},
});
</script>
