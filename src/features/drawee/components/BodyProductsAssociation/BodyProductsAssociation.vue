<template>
	<farm-container>
		<HeaderForm v-if="!isError" :data="dataHeaderForm" withLine />
		<TitlePageForm
			v-if="!isError"
			value="Produto"
			noPipe
			:label="!isEdit ? 'Adicionar' : 'Editar'"
		/>
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4">
				<farm-label for="form-address-type" required> Produto </farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="label"
					item-value="value"
					v-model="form.productId"
					:items="products"
					:rules="[rules.required]"
					:disabled="isEdit"
					@change="onChangeProduct"
				/>
			</farm-col>
			<farm-col cols="12" md="8" v-if="showFields">
				<div class="pt-5 d-flex">
					<farm-idcaption icon="clipboard-text-outline" copyText="">
						<template v-slot:subtitle>
							ID: {{ productInfo.id || 'Carregando...' }}
						</template>
					</farm-idcaption>
					<div class="header-edit-line"></div>
					<farm-idcaption icon="tag-outline" copyText="">
						<template v-slot:subtitle>
							Tipo: {{ productInfo.type || 'Carregando...' }}
						</template>
					</farm-idcaption>
					<div class="header-edit-line"></div>
					<farm-idcaption icon="flag-outline" copyText="">
						<template v-slot:subtitle> Status: {{ productInfo.status }} </template>
					</farm-idcaption>
				</div>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-address-type"> Carteira </farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="label"
					item-value="value"
					v-model="form.taxonomyWalletId"
					:items="wallets"
				></farm-select-auto-complete>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-address-type"> Classificação </farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="label"
					item-value="value"
					v-model="form.classificationId"
					:items="classifications"
				></farm-select-auto-complete>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-address-type"> Assinatura Mandato </farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="label"
					item-value="value"
					v-model="form.mandateSignature"
					:items="signatureMandates"
				></farm-select-auto-complete>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-register-end-date"> Data de Relacionamento </farm-label>
				<farm-input-datepicker
					ref="datepickerEndDate"
					inputId="form-register-end-date"
					v-model="form.relationshipDate"
					:max="maxDate()"
				/>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-address-type">
					Percentual de Concentração
					<farm-tooltip>
						Percentual de Concentração aprovado por exceção.
						<template v-slot:activator>
							<farm-icon size="sm" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</farm-label>
				<farm-textfield-v2
					id="form-address-zipcode"
					maxlength="4"
					v-model="form.typeList"
					:rules="[rules.percentage]"
				/>
			</farm-col>
		</farm-row>
		<farm-row class="mb-6">
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Onboarding </farm-label>
				<farm-switcher class="mt-4" id="" v-model="form.onboarding" block></farm-switcher>
			</farm-col>
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Adesão ao Programa </farm-label>
				<farm-switcher class="mt-4" id="" v-model="form.membership" block></farm-switcher>
			</farm-col>
			<farm-col cols="12" md="2" v-if="showFields">
				<farm-label for="form-address-type"> Liberação do Originador </farm-label>
				<farm-switcher
					class="mt-4"
					id=""
					v-model="form.releasedSource"
					block
				></farm-switcher>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT, PF } from '@/constants';
import TitlePageForm from '@/components/TitlePageForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { parsePayload, parseConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import { checkValueDatePicker } from '@/helpers/checkValueDatePicker';

import { signatureMandate } from '../../configurations/selects';
import { headersPages } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		TitlePageForm,
		FooterForm,
		HeaderForm,
	},
	props: {
		typeProduct: {
			type: String,
		},
	},
	data() {
		return {
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			labelButton: 'Associar',
			disabledButtonFooter: null,
			form: {
				productId: '',
				onboarding: false,
				membership: false,
				releasedSource: false,
				typeList: '',
				mandateSignature: '',
				classificationId: '',
				taxonomyWalletId: '',
				relationshipDate: '',
			},
			products: [],
			classifications: [],
			wallets: [],
			signatureMandates: signatureMandate,
			rules: {
				required: value => !!value || 'Campo obrigatório',
				percentage: value => {
					if (value.length === 0) {
						return true;
					}
					const REGEXP = /^(\d+,)*(\d+)$/;
					if (!REGEXP.test(value)) {
						return 'Valor inválido';
					}
					if (parseInt(value, 10) < 0 || parseInt(value, 10) > 100) {
						return 'Campo inválido';
					}
					return true;
				},
			},
			showFields: false,
			currentId: 0,
			currentIdProduct: 0,
			productInfo: {
				id: '',
				type: '',
				status: '',
			},
			maxLengthPorcentage: 3,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			draweeAssociationProductIdData: 'draweeAssociationProductIdData',
			draweeAssociationClassification: 'draweeAssociationClassification',
			draweeAssociationProduct: 'draweeAssociationProduct',
			draweeAssociationWallet: 'draweeAssociationWallet',
			draweeAssociationProductIdDataRequestStatus:
				'draweeAssociationProductIdDataRequestStatus',
			draweeAssociationClassificationRequestStatus:
				'draweeAssociationClassificationRequestStatus',
			draweeAssociationProductRequestStatus: 'draweeAssociationProductRequestStatus',
			draweeAssociationWalletRequestStatus: 'draweeAssociationWalletRequestStatus',
			draweeById: 'draweeById',
			draweeByIdRequestStatus: 'draweeByIdRequestStatus',
			saveDraweeAssociationProductsRequestStatus:
				'saveDraweeAssociationProductsRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeAssociationProductRequestStatus,
				this.draweeAssociationWalletRequestStatus,
				this.draweeAssociationClassificationRequestStatus,
				this.draweeAssociationProductIdDataRequestStatus,
				this.draweeByIdRequestStatus,
				this.saveDraweeAssociationProductsRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			return this.draweeAssociationProductRequestStatus.type === RequestStatusEnum.ERROR;
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
		currentType(): string {
			return this.$route.params.typeForm;
		},
		currentProduct(): string {
			return this.$route.params.product;
		},
		isEdit(): boolean {
			return this.typeProduct === EDIT;
		},
		isPF(): boolean {
			return this.$route.params.typeForm === PF.toLowerCase();
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.labelButton = 'Salvar';
		}
		this.load();
	},
	methods: {
		...mapActions('cadastros', {
			getByIdDraweeAssociationProduct: 'getByIdDraweeAssociationProduct',
			getDraweeNotAssociationClassification: 'getDraweeNotAssociationClassification',
			getDraweeNotAssociationProduct: 'getDraweeNotAssociationProduct',
			getDraweeNotAssociationWallet: 'getDraweeNotAssociationWallet',
			getDraweeById: 'getDraweeById',
			saveDraweeAssociationProducts: 'saveDraweeAssociationProducts',
		}),
		load(): void {
			this.getDraweeById({
				type: this.currentType.toUpperCase(),
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.load();
		},
		maxDate(): string {
			return new Date().toISOString();
		},
		onSave(): void {
			const payload = this.parsePaylod();
			this.saveDraweeAssociationProducts({
				draweeId: this.currentId,
				idProduct: this.currentIdProduct,
				type: this.isEdit ? 'edit' : 'new',
				payload,
			});
		},
		onCancel(): void {
			const document = this.currentDocument;
			const type = this.currentType;
			const path = `/admin/cadastros/sacados/${type}/${document}/editar?path=produtos`;
			this.$router.push({
				path,
			});
		},
		onChangeProduct(value): void {
			if (this.isEdit) {
				return;
			}
			if (value !== '') {
				this.currentIdProduct = value;
				this.callAPIsSelect(value);
				this.getDataProduct(value);
			} else {
				this.showFields = false;
				this.disabledButtonFooter = false;
			}
		},
		callAPIsSelect(id): void {
			this.showFields = true;
			this.disabledButtonFooter = true;
			const payload = {
				idProduct: id,
			};
			this.getDraweeNotAssociationWallet(payload);
			this.getDraweeNotAssociationClassification(payload);
		},
		updatedHeader(data): void {
			const key = this.isPF ? 'generalInfoPF' : 'generalInfoPJ';
			const title = this.isPF ? data[key].fullName : data[key].acronym;
			const raiz = this.isPF ? data[key].document : data[key].raiz;
			this.currentId = data[key].draweeId;
			const dataHeader = {
				title,
				listIcons: [
					data[key].peopleId,
					this.isPF ? 'Física' : 'Jurídica',
					[data[key].document, raiz],
				],
			};
			this.dataHeaderForm = parseDataHeader(dataHeader, headersPages);
		},
		updatedSelectProduct(data): void {
			this.products = data;
		},
		parsePaylod() {
			const classificationId = this.form.classificationId
				? parseInt(this.form.classificationId, 10)
				: null;
			const taxonomyWalletId = this.form.taxonomyWalletId
				? parseInt(this.form.taxonomyWalletId, 10)
				: null;
			const onboarding = this.form.onboarding ? 1 : 0;
			const mandateSignature = this.checkValueMandateSignature(this.form.mandateSignature);
			const membership = this.form.membership ? 1 : 0;
			const releasedSource = this.form.releasedSource ? 1 : 0;
			const productId = parseInt(this.form.productId, 10);

			const relationshipDate =
				this.form.relationshipDate === '' ? null : this.form.relationshipDate;

			const typeList = parsePayload(this.form.typeList);

			const payload = {
				typeList,
				classificationId,
				taxonomyWalletId,
				onboarding,
				mandateSignature,
				membership,
				releasedSource,
				relationshipDate,
			};
			if (this.isEdit) {
				return payload;
			}
			return {
				...payload,
				productId,
			};
		},
		updatedProductName(data): void {
			const newData = [data];
			this.updatedSelectProduct(newData);
		},
		updatedProductInfo(data): void {
			this.productInfo = data;
		},
		getDataProduct(id: string): void {
			const productSelected = this.draweeAssociationProduct.content.filter(item => {
				return item.id === parseInt(id, 10);
			});
			this.updatedProductInfo({
				id: productSelected[0].id,
				status: productSelected[0].status,
				type: productSelected[0].type,
			});
		},
		checkValueMandateSignature(value: string): number | null {
			if (value !== '') {
				return parseInt(value, 10) === 1 ? 1 : 0;
			}
			return null;
		},
		parseValueNullOrString(value): string | null {
			if (value !== null) {
				return value.toString();
			}
			return null;
		},
		parseMandateSignature(value: number | null): string {
			if (value === null) {
				return '';
			}
			return value.toString();
		},
	},
	watch: {
		draweeByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.draweeById.content);
				if (this.isEdit) {
					const payload = {
						idProduct: this.currentProduct,
						draweeId: this.currentId,
					};
					this.getDraweeNotAssociationWallet(payload);
					this.getDraweeNotAssociationClassification(payload);
					setTimeout(() => {
						this.getByIdDraweeAssociationProduct(payload);
					}, 200);
					return;
				}
				this.getDraweeNotAssociationProduct({
					id: this.currentId,
				});
			}
		},
		draweeAssociationProductRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedSelectProduct(this.draweeAssociationProduct.select);
			}
		},
		draweeAssociationProductIdDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const productSeleted = this.draweeAssociationProductIdData.content;
				const productId = this.parseValueNullOrString(productSeleted.productId);
				const classificationId = this.parseMandateSignature(
					productSeleted.classificationId
				);
				const taxonomyWalletId = this.parseMandateSignature(
					productSeleted.taxonomyWalletId
				);
				const relationshipDate = checkValueDatePicker(productSeleted.relationshipDate);
				this.form = {
					productId,
					onboarding: productSeleted.onboarding === 1,
					membership: productSeleted.membership === 1,
					releasedSource: productSeleted.releasedSource === 1,
					typeList: parseConcentrationPercentage(productSeleted.typeList, '', ''),
					mandateSignature: this.parseMandateSignature(productSeleted.mandateSignature),
					classificationId,
					taxonomyWalletId,
					relationshipDate,
				};

				this.updatedProductName({
					label: productSeleted.name,
					value: productSeleted.productId.toString(),
				});
				this.updatedProductInfo({
					id: productSeleted.productId,
					type: productSeleted.type,
					status: productSeleted.status,
				});
				this.currentIdProduct = this.currentProduct;
				this.showFields = true;
				this.disabledButtonFooter = true;
			}
		},
		draweeAssociationWalletRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.wallets = this.draweeAssociationWallet;
				if (this.draweeAssociationWallet.length === 1) {
					this.form.taxonomyWalletId = '';
				}
			}
		},
		draweeAssociationClassificationRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.classifications = this.draweeAssociationClassification;
				if (this.classifications.length === 1) {
					this.form.classificationId = '';
				}
			}
		},
		saveDraweeAssociationProductsRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				let mensage = '';
				if (this.isEdit) {
					mensage = `Associação ao produto atualizada com sucesso!`;
				} else {
					const productSeleted = this.draweeAssociationProduct.select.filter(item => {
						return item.value === this.currentIdProduct;
					});
					mensage = `Sacado associado com sucesso ao produto <b>${productSeleted[0].label}</b>.`;
				}
				notification(RequestStatusEnum.SUCCESS, mensage);
				setTimeout(() => {
					this.onCancel();
				}, 2000);
			}

			if (newValue.type === RequestStatusEnum.ERROR) {
				let productName = '';
				if (this.isEdit) {
					productName = this.draweeAssociationProductIdData.content.name;
				} else {
					const productSeleted = this.draweeAssociationProduct.select.filter(item => {
						return item.value === this.currentIdProduct;
					});
					productName = productSeleted[0].label;
				}
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível associar o produto <b>${productName}</b> ao sacado.`
				);
			}
		},
		'form.typeList': function (newValue): void {
			const REGEXP = /^(\d+,)*(\d+)$/;
			if (newValue.length > 0 && !REGEXP.test(newValue)) {
				this.disabledButtonFooter = false;
				return;
			}
			this.disabledButtonFooter = true;
		},
	},
});
</script>
<style scoped>
.header-edit-line {
	width: 1px;
	height: 40px;
	background-color: var(--farm-neutral-base);
	margin: 0 16px;
}
</style>
