export const sortAssociationProduct = [
	{ label: 'Associação Mais Recente - Menos Recente', value: 'startRelationship_DESC' },
	{ label: 'Associação Menos Recente - Mai<PERSON> Recente', value: 'startRelationship_ASC' },
	{ label: 'Fim de Associação Mais Recente - Menos Recente', value: 'endRelationship_DESC' },
	{ label: 'Fim de Associação Menos Recente - Mais Recente', value: 'endRelationship_ASC' },
	{ label: 'Alfabético A-Z', value: 'name_ASC' },
	{ label: 'Alfabético Z-A', value: 'name_DESC' },
];

export const sortHistory = [
	{ label: 'Mais Recente - Menos Recent<PERSON>', value: 'createdAt_DESC' },
	{ label: 'Menos Recente - Mais Recent<PERSON>', value: 'createdAt_ASC' },
];
