import {
	DATA,
	DOCUMENTS,
	PRODUCTS,
	CORPORATE_STRUCTURE,
	FOREIGNPARTICIPATION
} from '@/constants';
import { TabTypes } from '@/types';

export const tabDefault: Array<TabTypes> = [
	{
		name: 'dados',
		path: DATA,
	},
];

export const tabEdit: Array<TabTypes> = [
	...tabDefault,
	{
		name: 'Documentos',
		path: DOCUMENTS,
	},
	{
		name: 'Produtos',
		path: PRODUCTS,
	},
];

export const tabEditPJ: Array<TabTypes> = [
	...tabDefault,
	{
		name: 'Documento<PERSON>',
		path: DOCUMENTS,
	},
	{
		name: 'QUADRO SOCIETÁRIO',
		path: CORPORATE_STRUCTURE,
	},
	{
		name: 'PARTICIPAÇÃO ESTRANGEIRA',
		path: FOREIGNPARTICIPATION,
	},
	{
		name: 'Produtos',
		path: PRODUCTS,
	},
];
