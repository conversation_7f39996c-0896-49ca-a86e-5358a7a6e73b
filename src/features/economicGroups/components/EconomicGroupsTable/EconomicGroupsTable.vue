<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				class="elevation-0 mt-0 table-register-financial-vehicle"
				:headers="headers"
				:items="data"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader()"
				:options.sync="options"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
				</template>
				<template v-slot:header="{ props: { headers } }" v-if="showCustomHeader()">
					<farm-datatable-header
						firstSelected
						:headers="headers"
						:sortClick="sortClicked"
						:selectedIndex="2"
						@onClickSort="onSort"
					/>
				</template>

				<template v-slot:[`item.createdAt`]="{ item }">
					{{ defaultDateFormat(item.createdAt) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-context-menu
						:items="contextMenuItems(item)"
						@edit="editItem(item)"
						@remove="removeItem(item)"
					/>
				</template>

				<template v-slot:footer>
					<farm-datatable-paginator
						class="mt-6 mb-n6"
						:page="currentPagination"
						:totalPages="paginationTotalPages"
						@onChangePage="onChangePageTable"
						@onChangeLimitPerPage="onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	pageable,
	edit as editOption,
	remove as removeOption,
	defaultDateFormat,
	RequestStatusEnum,
	notificationWrapper,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import { headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
	},
	mixins: [pageable],
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
			currentPagination: 1,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			deleteEconomicGroupRequestStatus: 'deleteEconomicGroupRequestStatus',
		}),
		isLoading() {
			return this.deleteEconomicGroupRequestStatus === RequestStatusEnum.START;
		},
		breakpoint() {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		...mapActions('cadastros', {
			deleteEconomicGroup: 'deleteEconomicGroup',
		}),
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const parseOrderby = {
				createdAt: 'createdAt',
				name: 'name',
				clientsCount: 'clientsCount',
			};
			const filtersActive = {
				...this.filters,
				name: this.filter.name || '',
				orderby: parseOrderby[data.field],
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive, 1);
		},
		contextMenuItems(item) {
			if (!this.canWrite) {
				return [];
			}
			if (parseInt(item.clientsCount, 10) === 0) {
				return [editOption, removeOption];
			}
			return [editOption];
		},

		editItem(item): void {
			this.$router.push({
				path: `/admin/cadastros/grupos_economicos/${item.id}/editar`,
			});
		},
		removeItem(item) {
			this.$dialog
				.confirm(
					{
						body: `Deseja realmente remover o grupo ${stripTags(item.name)}?`,
						title: 'Remover',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.deleteEconomicGroup({ id: item.id });
				})
				.catch(() => {});
		},
		defaultDateFormat,
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filters.page = pageActive;
			this.currentPagination = page;
			this.$emit(
				'onRequest',
				{ page: pageActive, limit: this.filters.limit, search: this.filter.search || '' },
				this.currentPagination
			);
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filters.limit = limit;
			this.currentPagination = 1;
			this.$emit(
				'onRequest',
				{ page: 0, limit: limit, search: this.filter.search || '' },
				this.currentPagination
			);
		},
	},
	watch: {
		paginationPageActive(newValue): void {
			this.currentPagination = newValue;
		},
		deleteEconomicGroupRequestStatus(newValue): string {
			notificationWrapper(newValue, 'Grupo econômico excluído', 'excluir o grupo econômico');
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.$emit(
					'onRequest',
					{ page: 0, limit: this.filter.limit, search: this.filter.search || '' },
					this.currentPagination
				);
				return RequestStatusEnum.SUCCESS;
			}
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-register-financial-vehicle', 1, (0));
</style>
