<template>
	<farm-form v-model="valid">
		<farm-row v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-label for="form-economicGroups-name" required> Nome </farm-label>
				<farm-textfield-v2
					v-model="form.name"
					id="form-economicGroups-name"
					class="input-groups"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="2" v-if="isEdit">
				<farm-label for="form-economicGroups-id"> ID </farm-label>
				<farm-textfield-v2 v-model="form.id" id="form-economicGroups-id" disabled />
			</farm-col>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<AlertReload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import { EDIT, NEW } from '@/constants';
import FooterForm from '@/components/FooterForm';
import { requiredConfig } from '@/helpers/validators/required';
import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';
import storage from '@/helpers/storage';

import { TabDataFormTypes, TabDataTypes, tabDataFormModel } from '../../types';

export default defineComponent({
	components: {
		FooterForm,
	},
	props: {
		isEdit: {
			type: Boolean,
			required: true,
		},
	},
	data(): TabDataTypes {
		return {
			valid: false,
			form: createObject<TabDataFormTypes>(tabDataFormModel),
			postCreatedEconomicGroupId: '',
		};
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			economicGroupItem: 'economicGroupItem',
			economicGroupSave: 'economicGroupSave',
			economicGroupItemRequestStatus: 'economicGroupItemRequestStatus',
			economicGroupSaveRequestStatus: 'economicGroupSaveRequestStatus',
			economicGroupPostCreatedId: 'economicGroupPostCreatedId',
		}),
		isLoading(): boolean {
			const status = [
				this.economicGroupItemRequestStatus,
				this.economicGroupSaveRequestStatus,
			];
			return status.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const status = [this.economicGroupItemRequestStatus.type];
			return status.includes(RequestStatusEnum.ERROR);
		},
		rules() {
			return {
				required: requiredConfig('Nome do Grupo obrigatório'),
			};
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	mounted(): void {
		this.$emit('onSubmit', this.submit);
		if (this.isEdit) {
			this.load();
		}
	},
	methods: {
		...mapActions('cadastros', {
			getEconomicGroupsById: 'getEconomicGroupsById',
			saveEconomicGroup: 'saveEconomicGroup',
		}),
		load(): void {
			this.getEconomicGroupsById({
				id: this.currentId,
			});
		},
		reload(): void {
			this.load();
		},
		backToHome(): void {
			this.$router.push({
				path: `/admin/cadastros/grupos_economicos`,
			});
		},
		submit(): void {
			const id = this.isEdit ? this.currentId : 0;
			const type = this.isEdit ? EDIT : NEW;
			this.saveEconomicGroup({
				name: this.form.name,
				type,
				id,
			});
		},

		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/grupos_economicos/${this.postCreatedEconomicGroupId}/editar?path=dados`,
			});
			this.$router.go(0);
		},

		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.backToHome();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.reload();
				})
				.catch(() => {
					this.backToHome();
				});
		},
		updatePostCreatedEconomicGroupId() {
			this.postCreatedEconomicGroupId = this.economicGroupPostCreatedId.id;
		},
		createMessage(type: 'ERROR' | 'SUCCESS'): string {
			const message = {
				ERROR: this.isEdit
					? 'Erro ao cadastrar o Grupo Econômico.'
					: 'Erro ao atualizar o Grupo Econômico.',
				SUCCESS: this.isEdit
					? `Dados do Grupo Econômico atualizado com sucesso. Deseja continuar editando?`
					: `Grupo Econômico cadastrado com sucesso. Deseja continuar para a edição?`,
			};
			return message[type];
		},
	},
	watch: {
		valid(value): void {
			this.$emit('onDisabledButton', value);
		},
		economicGroupItemRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.form.id = this.economicGroupItem.content.id;
				this.form.name = this.economicGroupItem.content.name;
				const updatedData = format(this.economicGroupItem.meta);
				this.$emit('onUpdateDataUser', { ...updatedData });
				storage.clear();
				storage.set('id', this.economicGroupItem.content.id);
				storage.set('name', this.economicGroupItem.content.name);
				return RequestStatusEnum.SUCCESS;
			}
			return newValue;
		},
		economicGroupSaveRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.isEdit) {
					this.editDialog(this.createMessage('SUCCESS'));
					return;
				}
				this.updatePostCreatedEconomicGroupId();
				this.createDialog(this.createMessage('SUCCESS'));
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessage('ERROR'));
			}
		},
	},
});
</script>
<style>
.farm-textfield.v-input .v-input__slot #form-economicGroups-name {
	text-transform: uppercase;
}
</style>
