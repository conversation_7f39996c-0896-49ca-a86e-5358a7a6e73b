<template>
	<div class="mt-6">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<TitlePageForm label="Unir Grupos" :value="titlePage" />
			</farm-col>
			<farm-col cols="12" md="6">
				<JoinGroupsTable
					:data="dataTableJoinGroups"
					:filter="filterTableJoinGroups"
					:paginationTotalPages="paginationTotalPagesJoinGroups"
					:paginationPageActive="paginationPageActiveJoinGroups"
					:reset="resetTableJoinGroups"
					@onRequest="onRequestJoinGroups"
					@onUpdateItemSelect="onUpdateItemSelect"
				/>
			</farm-col>
			<farm-col cols="12" md="6">
				<JoinGroupsTableSelected
					:data="dataTableJoinGroupsSelected"
					:filter="filterTableJoinGroupsSelected"
					:paginationTotalPages="paginationTotalPagesJoinGroupsSelected"
					:paginationPageActive="paginationPageActiveJoinGroupsSelected"
					@onRequest="onRequestJoinGroupsSelected"
					@onUpdatedTotalPagesGroupsSelected="onUpdatedTotalPagesGroupsSelected"
					@onUpdatedGroupsSelectedRemoveItem="onUpdatedGroupsSelectedRemoveItem"
					@onUpdatedGroupsSelectedRemoveAll="onUpdatedGroupsSelectedRemoveAll"
					@onUpdatedGroupsSelectedPagination="onUpdatedGroupsSelectedPagination"
				/>
			</farm-col>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<AlertReload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	notification,
	defaultDateFormat,
} from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';
import storage from '@/helpers/storage';

import JoinGroupsTable from '../JoinGroupsTable';
import JoinGroupsTableSelected from '../JoinGroupsTableSelected';

export default defineComponent({
	components: {
		TitlePageForm,
		JoinGroupsTable,
		JoinGroupsTableSelected,
	},
	data() {
		return {
			titlePage: '',
			itemsSelected: [],
			itemsRemoveSelected: [],
			itemsRemoveAll: false,
			dataTableJoinGroups: [],
			paginationTotalPagesJoinGroups: 0,
			paginationPageActiveJoinGroups: 1,
			filterTableJoinGroups: {
				page: 0,
				limit: 10,
			},
			resetTableJoinGroups: false,
			dataTableJoinGroupsSelected: [],
			dataTableJoinGroupsSelectedCache: [],
			paginationTotalPagesJoinGroupsSelected: 0,
			paginationPageActiveJoinGroupsSelected: 1,
			filterTableJoinGroupsSelected: {
				page: 0,
				limit: 10,
			},
		};
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			economicGroupJoin: 'economicGroupJoin',
			economicGroupJoinRequestStatus: 'economicGroupJoinRequestStatus',
			economicGroupJoinSaveRequestStatus: 'economicGroupJoinSaveRequestStatus',
		}),
		isLoading() {
			return (
				this.economicGroupJoinRequestStatus === RequestStatusEnum.START ||
				this.economicGroupJoinSaveRequestStatus === RequestStatusEnum.START
			);
		},
		isError() {
			return this.economicGroupJoinRequestStatus.type === RequestStatusEnum.ERROR;
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	mounted(): void {
		this.$emit('onSubmit', this.submit);
		this.$emit('onDisabledButton', false);
		this.titlePage = storage.get('name');
		this.load();
	},
	methods: {
		...mapActions('cadastros', {
			getToJoinEconomicGroups: 'getToJoinEconomicGroups',
			saveMergeEconomicGroup: 'saveMergeEconomicGroup',
		}),

		load(): void {
			this.getToJoinEconomicGroups({
				filters: {
					...this.filterTableJoinGroups,
				},
				id: this.currentId,
			});
		},
		reload(): void {
			this.load();
		},
		submit() {
			this.resetTableJoinGroups = true;
			const ids = this.dataTableJoinGroupsSelected.map(item => item.id);
			const payload = {
				groupId: this.currentId,
				groupIds: [...ids],
			};
			this.saveMergeEconomicGroup({ payload });
		},
		backToHome(): void {
			setTimeout(() => {
				this.$router.push({
					path: `/admin/cadastros/grupos_economicos`,
				});
			}, 1500);
		},
		isNumber(value): boolean {
			return !window.isNaN(value);
		},
		searchDataTable(value: string, itemName: string): boolean {
			return itemName.toUpperCase().search(value.toUpperCase()) > -1;
		},
		orderName(data, type) {
			const result = data.sort((a, b) => {
				let nameA = a.name.toUpperCase();
				let nameB = b.name.toUpperCase();
				return nameA === nameB ? 0 : nameA > nameB ? 1 : -1;
			});
			if (type === 'DESC') {
				return result.reverse();
			}
			return result;
		},
		orderDate(data, type) {
			const dataWithNewDate = data.map(item => {
				if (item.createdAt.length > 0) {
					let date = defaultDateFormat(item.createdAt);
					let [day, month, year] = date.split('/');
					return {
						...item,
						createdAt: new Date(parseInt(year), parseInt(month, 10) - 1, parseInt(day)),
					};
				}
				return item;
			});
			const result = dataWithNewDate.sort((a, b) => {
				return a === b ? 0 : a > b ? 1 : -1;
			});
			if (type === 'DESC') {
				return result.reverse();
			}
			return result;
		},
		onRequestJoinGroups(dataFilter, page) {
			this.paginationPageActiveJoinGroups = page;
			this.getToJoinEconomicGroups({
				filters: {
					...dataFilter,
				},
				id: this.currentId,
			});
		},
		onRequestJoinGroupsSelected(newValue, pageActive) {
			if (newValue.search.length > 0) {
				const key = this.isNumber(newValue.search) ? 'id' : 'name';
				const dataWithFilter = this.dataTableJoinGroupsSelectedCache.filter(item => {
					if (key === 'id') {
						return item[key] === parseInt(newValue.search, 10);
					}
					return this.searchDataTable(newValue.search, item[key]);
				});
				const start = newValue.page * this.filterTableJoinGroupsSelected.limit;
				const end = this.filterTableJoinGroupsSelected.limit + start;
				const dataShowTable = dataWithFilter.slice(start, end);
				this.dataTableJoinGroupsSelected = [...dataShowTable];
				const totalPagesCalc = Math.ceil(
					dataWithFilter.length / this.filterTableJoinGroupsSelected.limit
				);
				this.paginationTotalPagesJoinGroupsSelected = totalPagesCalc;
			} else if (newValue.orderby.length > 0) {
				let dataOrder =
					newValue.orderby === 'name'
						? this.orderName(this.dataTableJoinGroupsSelectedCache, newValue.order)
						: this.orderDate(this.dataTableJoinGroupsSelectedCache, newValue.order);

				const start = newValue.page * this.filterTableJoinGroupsSelected.limit;
				const end = this.filterTableJoinGroupsSelected.limit + start;
				const dataShowTable = dataOrder.slice(start, end);

				const totalPagesCalc = Math.ceil(
					dataOrder.length / this.filterTableJoinGroupsSelected.limit
				);
				this.dataTableJoinGroupsSelected = [...dataShowTable];
				this.paginationTotalPagesJoinGroupsSelected = totalPagesCalc;
			} else {
				const start = newValue.page * this.filterTableJoinGroupsSelected.limit;
				const end = this.filterTableJoinGroupsSelected.limit + start;
				const dataShowTable = this.dataTableJoinGroupsSelectedCache.slice(start, end);
				this.dataTableJoinGroupsSelected = [...dataShowTable];
				const totalPagesCalc = Math.ceil(
					this.dataTableJoinGroupsSelectedCache.length /
						this.filterTableJoinGroupsSelected.limit
				);
				this.paginationTotalPagesJoinGroupsSelected = totalPagesCalc;
			}
			this.filterTableJoinGroupsSelected = {
				...newValue,
			};
			this.paginationPageActiveJoinGroupsSelected = pageActive;
		},
		onUpdatedTotalPagesGroupsSelected(newValue) {
			this.filterTableJoinGroupsSelected.limit = newValue;
		},
		onUpdatedGroupsSelectedPagination(data) {
			this.paginationTotalPagesJoinGroupsSelected = data.paginationTotalPages;
			this.filterTableJoinGroupsSelected.limit = data.limit;
		},
		onUpdateItemSelect(data): void {
			const ids = this.dataTableJoinGroupsSelected.map(item => item.id);
			const dataCheck = data.filter(item => !ids.includes(item.id));
			const dataMerge = this.dataTableJoinGroupsSelected.concat(dataCheck);
			const unique = [...new Set(dataMerge)];
			this.itemsSelected = dataCheck;
			this.dataTableJoinGroupsSelectedCache = [...unique];

			const start =
				(this.paginationPageActiveJoinGroupsSelected - 1) *
				this.filterTableJoinGroupsSelected.limit;
			const end = this.filterTableJoinGroupsSelected.limit + start;

			const dataShowTable = this.dataTableJoinGroupsSelectedCache.slice(start, end);
			this.dataTableJoinGroupsSelected = [...dataShowTable];

			const totalPagesCalc = Math.ceil(
				this.dataTableJoinGroupsSelectedCache.length /
					this.filterTableJoinGroupsSelected.limit
			);
			this.paginationTotalPagesJoinGroupsSelected = totalPagesCalc;
			this.$emit('onDisabledButton', true);
		},
		onUpdatedGroupsSelectedRemoveItem(data) {
			this.itemsRemoveSelected = data.itemRemoved;
			this.dataTableJoinGroupsSelected = data.newDataTable;
			const ids = this.itemsSelected.map(item => item.id);
			const itemsRemoveSelectedUpdated = this.itemsRemoveSelected.filter(
				item => !ids.includes(item.id)
			);
			const idsRemoves = this.itemsRemoveSelected.map(item => item.id);
			const itemsSelectedUpdated = this.itemsSelected.filter(
				item => !idsRemoves.includes(item.id)
			);
			this.itemsRemoveSelected = itemsRemoveSelectedUpdated;
			this.itemsSelected = itemsSelectedUpdated;
			let statusDisabledButton = true;
			if (
				!this.itemsRemoveAll &&
				this.itemsRemoveSelected.length === 0 &&
				this.itemsSelected.length === 0
			) {
				statusDisabledButton = false;
			}

			const dataCacheUpdated = this.dataTableJoinGroupsSelectedCache.filter(
				item => !idsRemoves.includes(item.id)
			);
			this.dataTableJoinGroupsSelectedCache = [...dataCacheUpdated];

			const totalPagesCalc = Math.ceil(
				this.dataTableJoinGroupsSelectedCache.length /
					this.filterTableJoinGroupsSelected.limit
			);
			this.paginationTotalPagesJoinGroupsSelected = totalPagesCalc;
			this.$emit('onDisabledButton', statusDisabledButton);
		},
		onUpdatedGroupsSelectedRemoveAll(data) {
			this.itemsRemoveAll = data;
			this.dataTableJoinGroupsSelected = [];
			this.dataTableJoinGroupsSelectedCache = [];
			this.$emit('onDisabledButton', true);
		},
	},
	watch: {
		economicGroupJoinRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataTableJoinGroups = this.economicGroupJoin.content;
				this.paginationTotalPagesJoinGroups = this.economicGroupJoin.totalPages;
				return RequestStatusEnum.SUCCESS;
			}
			return newValue;
		},
		economicGroupJoinSaveRequestStatus(newValue) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(RequestStatusEnum.SUCCESS, 'União de grupos realizada com sucesso!');
				this.backToHome();
				return RequestStatusEnum.SUCCESS;
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao tentar unir o grupo: ' + newValue.message
				);
				return RequestStatusEnum.ERROR;
			}
			return newValue;
		},
	},
});
</script>
