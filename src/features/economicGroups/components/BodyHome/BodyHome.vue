<template>
	<farm-container>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col md="6" sm="12" cols="12" lg="6">
				<farm-form-mainfilter
					label="Buscar Grupo Econômico"
					:hasExtraFilters="false"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col align="right" md="6" sm="12" cols="12" lg="6">
				<farm-btn-confirm
					v-if="canWrite"
					class="farm-btn--responsive mt-8"
					title="Adicionar Grupo Econômico"
					customIcon="plus"
					to="/admin/cadastros/grupos_economicos/novo"
					:icon="true"
				>
					Adicionar Grupo Econômico
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<EconomicGroupsTable
			v-if="!isError"
			:data="dataTable"
			:paginationTotalPages="paginationTotalPages"
			:paginationPageActive="currentPage"
			:filter="filterTable"
			@onRequest="onRequest"
		/>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, pageable } from '@farm-investimentos/front-mfe-libs-ts';

import EconomicGroupsTable from '../EconomicGroupsTable';
import ModalAssociateAnotherGroup from '../ModalAssociateAnotherGroup';

export default defineComponent({
	components: {
		EconomicGroupsTable,
		ModalAssociateAnotherGroup,
	},
	mixins: [pageable],
	data() {
		return {
			lastSearchFilters: {},
			filter: false,
			filterInputKey: 'search',
			filters: {
				search: '',
				page: 0,
				limit: 10,
			},
			filterTable: {},
			dataTable: [],
			RequestStatusEnum,
			paginationTotalPages: 0,
			isModal: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			economicGroupList: 'economicGroupList',
			economicGroupRequestStatus: 'economicGroupRequestStatus',
		}),
		isLoading(): boolean {
			return this.economicGroupRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.economicGroupRequestStatus.type === RequestStatusEnum.ERROR;
		},
	},
	mounted(): void {
		this.doSearch();
	},

	methods: {
		...mapActions('cadastros', {
			getEconomicGroups: 'getEconomicGroups',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getEconomicGroups({
				idProduct: this.selectedProduct.id,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		reload(): void {
			this.doSearch();
		},
		onRequest(filtersActive, page): void {
			this.filters = {
				...this.filters,
				...filtersActive,
				name: this.filters.name,
				page: page - 1,
			};
			this.doSearch();
		},
	},
	watch: {
		economicGroupRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataTable = this.economicGroupList.content;
				this.paginationTotalPages = this.economicGroupList.totalPages;
				return RequestStatusEnum.SUCCESS;
			}
			return newValue;
		},
	},
});
</script>
