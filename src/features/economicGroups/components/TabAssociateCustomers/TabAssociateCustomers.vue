<template>
	<div class="mt-6">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<TitlePageForm label="Associar Clientes" :value="titlePage" />
			</farm-col>
			<farm-col cols="12" md="6">
				<CustomersTable
					:data="dataTableCustomers"
					:filter="filterTableCustomers"
					:paginationTotalPages="paginationTotalPagesCustomers"
					:paginationPageActive="paginationPageActiveCustomers"
					@onRequest="onRequestCustomers"
					@onUpdateItemSelect="onUpdateItemSelect"
				/>
			</farm-col>
			<farm-col cols="12" md="6">
				<CustomersTableSelected
					:data="dataTableCustomersSelected"
					:filter="filterTableCustomersSelected"
					:paginationTotalPages="paginationTotalPagesCustomersSelected"
					:paginationPageActive="paginationPageActiveCustomersSelected"
					:customersSelected="itemsSelected"
					@onRequest="onRequestCustomersSelected"
					@onUpdatedCustomersSelectedRemoveItem="onUpdatedCustomersSelectedRemoveItem"
					@onUpdatedCustomersSelectedRemoveAll="onUpdatedCustomersSelectedRemoveAll"
					@onUpdatedTotalPagesCustomersSelected="onUpdatedTotalPagesCustomersSelected"
				/>
			</farm-col>
		</farm-row>

		<ModalAssociateAnotherGroup
			v-if="modalAssociateAnotherGroup"
			v-model="modalAssociateAnotherGroup"
			:quant="quantModal"
			:data="dataModal"
			@onConfirm="onConfirmModal"
		/>
		<farm-loader mode="overlay" v-if="isLoading && !isError" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';
import storage from '@/helpers/storage';
import { format } from '@/helpers/formatUpdateUser';

import CustomersTable from '../CustomersTable';
import CustomersTableSelected from '../CustomersTableSelected';
import ModalAssociateAnotherGroup from '../ModalAssociateAnotherGroup';

export default defineComponent({
	components: {
		TitlePageForm,
		CustomersTable,
		CustomersTableSelected,
		ModalAssociateAnotherGroup,
	},
	data() {
		return {
			dataUpdated: {},
			titlePage: '',
			itemsSelected: [],
			itemsRemoveSelected: [],
			itemsRemoveAll: false,
			filterTableCustomers: {
				page: 0,
				limit: 10,
			},
			dataTableCustomers: [],
			paginationTotalPagesCustomers: 0,
			paginationPageActiveCustomers: 1,
			filterTableCustomersSelected: {
				page: 0,
				limit: 10,
			},
			dataTableCustomersSelected: [],
			paginationTotalPagesCustomersSelected: 0,
			paginationPageActiveCustomersSelected: 1,
			loadingFirstRender: true,
			modalAssociateAnotherGroup: false,
			quantModal: 0,
			dataModal: [],
		};
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			tableCustomersData: 'tableCustomersData',
			tableCustomersSelectedData: 'tableCustomersSelectedData',
			customersAssociation: 'customersAssociation',
			tableCustomersRequestStatus: 'tableCustomersRequestStatus',
			tableCustomersSelectedRequestStatus: 'tableCustomersSelectedRequestStatus',
			associateCustomersSaveRequestStatus: 'associateCustomersSaveRequestStatus',
		}),
		isLoading() {
			if (this.loadingFirstRender) {
				return !this.firstRenderLoading;
			}
			return (
				this.tableCustomersRequestStatus === RequestStatusEnum.START ||
				this.tableCustomersSelectedRequestStatus === RequestStatusEnum.START ||
				this.associateCustomersSaveRequestStatus === RequestStatusEnum.START
			);
		},
		firstRenderLoading() {
			return (
				this.tableCustomersRequestStatus === RequestStatusEnum.SUCCESS &&
				this.tableCustomersSelectedRequestStatus === RequestStatusEnum.SUCCESS
			);
		},
		isError() {
			return (
				this.tableCustomersRequestStatus.type === RequestStatusEnum.ERROR ||
				this.tableCustomersSelectedRequestStatus.type === RequestStatusEnum.ERROR
			);
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	mounted(): void {
		this.$emit('onSubmit', this.submit);
		this.$emit('onDisabledButton', false);
		this.titlePage = storage.get('name');
		this.load();
	},
	methods: {
		...mapActions('cadastros', {
			getCustomersEconomicGroup: 'getCustomersEconomicGroup',
			getCustomersSelectedEconomicGroup: 'getCustomersSelectedEconomicGroup',
			postAddEconomicGroup: 'postAddEconomicGroup',
		}),
		load(): void {
			this.$emit('onDisabledButton', false);
			this.loadingFirstRender = true;
			this.getCustomersEconomicGroup({
				filters: {
					...this.filterTableCustomers,
				},
				id: this.currentId,
			});
			this.getCustomersSelectedEconomicGroup({
				filters: {
					...this.filterTableCustomersSelected,
				},
				id: this.currentId,
			});
		},
		reload(): void {
			this.load();
		},
		onConfirmModal(): void {
			this.itemsSelected = [];
			this.itemsRemoveSelected = [];
			this.itemsRemoveAll = false;
			setTimeout(() => {
				this.backToHome();
			}, 2000);
		},
		createPayloadAddSelected() {
			return this.dataTableCustomersSelected.map(item => item.id);
		},
		createPayloadRemoveSelected() {
			return this.itemsRemoveSelected.map(el => el.id);
		},

		submit(): void {
			const payload = {
				add: this.createPayloadAddSelected(),
				remove: this.createPayloadRemoveSelected(),
				groupId: this.currentId,
				removeAll: this.itemsRemoveAll,
			};
			this.loadingFirstRender = false;
			this.postAddEconomicGroup({ payload });
		},
		backToHome(): void {
			this.$router.push({
				path: `/admin/cadastros/grupos_economicos`,
			});
		},
		onRequestCustomers(data, page): void {
			this.paginationPageActiveCustomers = page;
			this.loadingFirstRender = false;
			this.getCustomersEconomicGroup({
				filters: {
					...data,
				},
				id: this.currentId,
			});
		},
		onRequestCustomersSelected(data, page): void {
			this.paginationPageActiveCustomersSelected = page;
			this.loadingFirstRender = false;
			this.getCustomersSelectedEconomicGroup({
				filters: {
					...data,
				},
				id: this.currentId,
			});
		},
		onUpdateItemSelect(data): void {
			const ids = this.dataTableCustomersSelected.map(item => item.id);
			const dataCheck = data.filter(item => !ids.includes(item.id));
			const dataMerge = [...dataCheck, ...this.dataTableCustomersSelected];
			const unique = [...new Set(dataMerge)];
			this.itemsSelected = dataCheck;
			this.dataTableCustomersSelected = [...unique];

			this.$emit('onDisabledButton', true);
		},
		onUpdatedCustomersSelectedRemoveItem(data) {
			this.itemsRemoveSelected = data.itemRemoved;
			this.dataTableCustomersSelected = data.newDataTable;

			const ids = this.itemsSelected.map(item => item.id);
			const itemsRemoveSelectedUpdated = this.itemsRemoveSelected.filter(
				item => !ids.includes(item.id)
			);
			const idsRemoves = this.itemsRemoveSelected.map(item => item.id);
			const itemsSelectedUpdated = this.itemsSelected.filter(
				item => !idsRemoves.includes(item.id)
			);
			this.itemsRemoveSelected = itemsRemoveSelectedUpdated;
			this.itemsSelected = itemsSelectedUpdated;
			let statusDisabledButton = true;
			if (
				!this.itemsRemoveAll &&
				this.itemsRemoveSelected.length === 0 &&
				this.itemsSelected.length === 0
			) {
				statusDisabledButton = false;
			}
			this.$emit('onDisabledButton', statusDisabledButton);
		},
		onUpdatedCustomersSelectedRemoveAll(data) {
			this.itemsRemoveAll = data;
			this.dataTableCustomersSelected = [];
			this.$emit('onDisabledButton', true);
		},
		onUpdatedTotalPagesCustomersSelected(newValue) {
			this.filterTableCustomersSelected.limit = newValue;
		},
		formateCustomers(quant): string {
			const text = quant === 1 ? 'Cliente' : 'Cliente(s)';
			return `${quant} ${text}`;
		},
	},
	watch: {
		tableCustomersRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataTableCustomers = this.tableCustomersData.content;
				this.paginationTotalPagesCustomers = this.tableCustomersData.totalPages;
				return RequestStatusEnum.SUCCESS;
			}
			return newValue;
		},
		tableCustomersSelectedRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataTableCustomersSelected = this.tableCustomersSelectedData.content;
				this.paginationTotalPagesCustomersSelected =
					this.tableCustomersSelectedData.totalPages;
				const updatedData = format(this.tableCustomersSelectedData.meta);
				this.$emit('onUpdateDataUser', updatedData);
				return RequestStatusEnum.SUCCESS;
			}
			return newValue;
		},
		associateCustomersSaveRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const onlyRemove = this.customersAssociation.content.onlyRemove;
				if (onlyRemove) {
					notification(RequestStatusEnum.SUCCESS, 'Clientes removidos do grupo');
					setTimeout(() => {
						this.backToHome();
					}, 3500);
					return;
				}
				const hasUserGroup = this.customersAssociation.notAddedClients.length;
				if (hasUserGroup > 0) {
					this.quantModal = this.customersAssociation.addedClients.length;
					this.dataModal = this.customersAssociation.notAddedClients;
					this.modalAssociateAnotherGroup = true;
				} else {
					const quantClients = this.customersAssociation.addedClients.length;
					this.quantModal = quantClients;
					this.modalSuccess = true;

					notification(
						RequestStatusEnum.SUCCESS,
						`Associação ao grupo realizado com sucesso para<b>{{ ${this.formateCustomers(
							quantClients
						)} }}</b>!`
					);

					setTimeout(() => {
						this.itemsSelected = [];
						this.itemsRemoveSelected = [];
						this.itemsRemoveAll = false;
						this.backToHome();
					}, 3500);
				}
				return RequestStatusEnum.SUCCESS;
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao tentar Associar: ' + newValue.message
				);
				return RequestStatusEnum.ERROR;
			}
			return newValue;
		},
	},
});
</script>
