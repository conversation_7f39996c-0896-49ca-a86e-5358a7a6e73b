<template>
	<div>
		<farm-row>
			<farm-col cols="12">
				<farm-label for="form-customers"> Grupos Selecionados </farm-label>
				<farm-textfield-v2 v-model="input" id="form-customers" @keyup="onKeyUp" />
				<div class="d-flex justify-end mb-6">
					<farm-btn-confirm
						dense
						class="farm-btn--responsive"
						title="Remover selecionados"
						:disabled="selectItem.length === 0"
						@click="onHandleRemoveItem"
					>
						Remover selecionados
					</farm-btn-confirm>
					<farm-btn-confirm
						dense
						class="farm-btn--responsive ml-2"
						title="Remover todos"
						:disabled="selectItem.length === 0"
						@click="onHandleRemoveAll"
					>
						Remover todos
					</farm-btn-confirm>
				</div>
			</farm-col>
		</farm-row>
		<v-data-table
			class="elevation-0 pb-4 mt-0"
			id="table-customers-jointable-selected"
			item-key="id"
			hide-default-footer
			v-model="selectItem"
			:headers="headers"
			:items="data"
			:show-select="true"
			:server-items-length="data.length"
			:hide-default-header="showCustomHeader()"
			:options.sync="options"
			:header-props="headerProps"
		>
			<template slot="no-data">
				<DataTableEmptyWrapper
					subtitle="Adicione os clientes selecionados para associar ao grupo."
				/>
			</template>

			<template #header="{ props, on }" v-if="showCustomHeader()">
				<DataTableHeader
					firstSelected
					:headers="props.headers"
					:sortClick="sortClicked"
					:selectedIndex="1"
					@onClickSort="onSort"
					v-model="props.everyItem"
					:headerProps="props"
					@toggleSelectAll="on['toggle-select-all']"
				/>
			</template>

			<template v-slot:[`item.createdAt`]="{ item }">
				{{ defaultDateFormat(item.createdAt) }}
			</template>

			<template v-slot:footer>
				<DataTablePaginator
					class="mt-6"
					:page="currentPagination"
					:totalPages="paginationTotalPages"
					@onChangePage="onChangePageTable"
					@onChangeLimitPerPage="onChangeLimitPerPageTable"
				/>
			</template>
		</v-data-table>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { pageable, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { headerGroupsSelected as headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
		customersSelected: {
			type: Array,
			default: () => [],
		},
	},
	mixins: [pageable],
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
			currentPagination: 1,
			selectItem: [],
			input: '',
			defaultDateFormat,
			inputEmpty: false,
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const filtersActive = {
				...this.filters,
				search: this.filter.search || '',
				orderby: data.field,
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive, 1);
		},

		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filters.page = pageActive;
			this.currentPagination = page;
			this.$emit(
				'onRequest',
				{
					page: pageActive,
					limit: this.filters.limit,
					search: this.filter.search || '',
					orderby: '',
					order: '',
				},
				this.currentPagination
			);
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filters.limit = limit;
			this.currentPagination = 1;
			this.$emit('onUpdatedTotalPagesGroupsSelected', limit);
			this.$emit(
				'onRequest',
				{ page: 0, limit: limit, search: this.filter.search || '', orderby: '', order: '' },
				this.currentPagination
			);
		},
		onHandleRemoveItem() {
			const ids = this.selectItem.map(el => el.id);
			const dataUpdated = this.data.filter(el => !ids.includes(el.id));
			this.$emit('onUpdatedGroupsSelectedRemoveItem', {
				itemRemoved: this.selectItem,
				newDataTable: dataUpdated,
			});
		},
		onHandleRemoveAll() {
			this.selectItem = [];
			this.$emit('onUpdatedGroupsSelectedRemoveAll', true);
		},
		onKeyUp() {
			if (this.input.length > 2) {
				this.inputEmpty = true;
				if (this.timer) {
					clearTimeout(this.timer);
					this.timer = null;
				}
				this.timer = setTimeout(() => {
					this.$emit(
						'onRequest',
						{
							...this.filter,
							page: 0,
							search: this.input,
							orderby: '',
							order: '',
						},
						this.currentPagination
					);
				}, 750);
				return false;
			}
			if (this.input.length === 0 && this.inputEmpty) {
				this.inputEmpty = false;
				this.$emit(
					'onRequest',
					{
						...this.filter,
						page: 0,
						search: '',
						orderby: '',
						order: '',
					},
					this.currentPagination
				);
			}
		},
	},
	watch: {
		paginationPageActive(newValue): void {
			this.currentPagination = newValue;
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-customers-jointable-selected', 1, (0));
</style>

<style lang="scss" scoped>
@import './JoinGroupsTableSelected.scss';
</style>
