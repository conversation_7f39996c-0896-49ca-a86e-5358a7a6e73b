<template>
	<farm-row>
		<farm-col cols="12" sm="12" md="4">
			<farm-label for="form-type" required> Tipo </farm-label>
			<farm-select-auto-complete
				id="form-type"
				v-model="form.type"
				:items="dataSelect"
				item-text="label"
				item-value="value"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-name" required> Nome </farm-label>
			<farm-textfield-v2
				uppercase
				v-model="form.name"
				id="form-name"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-document" required> CNPJ </farm-label>
			<farm-textfield-v2
				id="form-document"
				v-model="form.document"
				mask="##.###.###/####-##"
				:rules="[rules.required, rules.validate]"
			/>
		</farm-col>
		<farm-col cols="12" sm="12" md="4">
			<farm-label for="form-register-start-date" required> Data de início </farm-label>
			<farm-input-datepicker
				ref="datepickerStartDate"
				inputId="form-register-start-date"
				v-model="form.startDate"
				:required="true"
				:max="maxDate()"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" sm="12" md="4" v-if="isEdit">
			<farm-label for="form-register-end-date"> Data de fim</farm-label>
			<farm-input-datepicker
				ref="datepickerEndDate"
				inputId="form-register-end-date"
				v-model="form.endDate"
				:min="endDate(form.endDate)"
			/>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { isValidCNPJ } from '@farm-investimentos/front-mfe-libs-ts';

import { cleanDocument } from '@/helpers/masks';

type SplitDateType = {
	year: number;
	month: number;
	day: number;
};

export default defineComponent({
	props: {
		form: {
			type: Object,
		},
		dataSelect: {
			type: Array,
			default: () => [],
		},
		isEdit: {
			type: Boolean,
		},
	},
	data() {
		return {
			rules: {
				required: value => !!value || 'Campo obrigatório',
				validate: value => {
					const dataValue = cleanDocument(value);
					return isValidCNPJ(dataValue) ? true : 'CNPJ inválido';
				},
			},
		};
	},
	methods: {
		splitDate(date: string): SplitDateType {
			const [year, month, day] = date.split('-');
			return {
				year: parseInt(year, 10),
				month: parseInt(month, 10),
				day: parseInt(day, 10),
			};
		},
		endDate(dateInput: string): string {
			if (dateInput.length > 0) {
				const { year, month, day } = this.splitDate(dateInput);
				const dateAPI = new Date(year, month - 1, day);
				const dateNow = new Date();
				if (dateAPI > dateNow) {
					return new Date().toISOString();
				}
				return new Date(year, month - 1, day).toISOString();
			}
			return new Date().toISOString();
		},
		maxDate(): string {
			return new Date().toISOString();
		},
	},
});
</script>
