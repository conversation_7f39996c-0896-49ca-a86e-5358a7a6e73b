<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Fornecedor"
					:showFilters="filter"
					@onInputChange="filterInputChanged"
					@onClick="showFilters"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					class="mt-8"
					customIcon="plus"
					title="Associar Fornecedor"
					:to="`/admin/cadastros/veiculos_financeiros/${currentId}/associacao_fornecedor/novo`"
					:icon="true"
				>
					Associar Fornecedor
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<ProviderFilter key="filters" v-show="filter" @onApply="searchListener" />
		</collapse-transition>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<ProviderList
			v-if="!isError"
			:data="dataList"
			@handleFinalizeAssociation="handleFinalizeAssociation"
			@handleReAssociateOption="handleReAssociateOption"
		/>
		<farm-row extra-decrease v-if="!isError">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-prompt-user
			v-model="showModal"
			title="Finalizar Associação"
			subtitle=""
			match="REMOVER"
			@onConfirm="onConfirm"
			@onClose="onClose"
		>
			<template v-slot:subtitle>
				<farm-typography size="md" class="mt-6">
					Deseja realmente finalizar a associação com o fornecedor
				</farm-typography>
				<farm-typography bold size="md"> {{ dataSelected.name }}?</farm-typography>
				<farm-typography size="md" class="mt-3">
					Escreva no campo abaixo
					<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography> para
					confirmar o fim da associação.
				</farm-typography>
			</template>
		</farm-prompt-user>
		<farm-col cols="12" v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</farm-col>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	RequestStatusEnum,
	pageable,
	notification,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';
import { format } from '@/helpers/formatUpdateUser';

import { providerSortOptions } from '../../configurations/sorts';
import ProviderList from '../ProviderList';
import ProviderFilter from '../ProviderFilter';

export default defineComponent({
	components: {
		ProviderList,
		ProviderFilter,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			lastSearchFilters: { page: 0, limit: 10 },
			filterInputKey: 'search',
			sortOptions: providerSortOptions,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			dataList: [],
			totalPages: 0,
			dataSelected: null,
			showModal: false,
			filter: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehicleProviderList: 'financialVehicleProviderList',
			financialVehicleProviderRequestStatus: 'financialVehicleProviderRequestStatus',
			financialVehicleHeaderData: 'financialVehicleHeaderData',
			financialVehicleHeaderRequestStatus: 'financialVehicleHeaderRequestStatus',
			reAssociateProviderRequestStatus: 'reAssociateProviderRequestStatus',
			deleteAssociateProviderRequestStatus: 'deleteAssociateProviderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatuses = [
				this.financialVehicleProviderRequestStatus,
				this.financialVehicleHeaderRequestStatus,
				this.reAssociateProviderRequestStatus,
				this.deleteAssociateProviderRequestStatus,
			];
			return requestStatuses.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatuses = [this.financialVehicleProviderRequestStatus.type];
			return requestStatuses.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	mounted(): void {
		this.getFinancialVehicleHeaders({
			financialVehicleId: this.currentId,
		});
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicleProviders: 'getFinancialVehicleProviders',
			getFinancialVehicleHeaders: 'getFinancialVehicleHeaders',
			reAssociateProvider: 'reAssociateProvider',
			deleteAssociateProvider: 'deleteAssociateProvider',
		}),
		reload(): void {
			this.doSearch();
		},
		reloadPage() {
			setTimeout(() => {
				this.doSearch();
			}, 2000);
		},
		onClose(): void {
			this.showModal = false;
		},
		onConfirm() {
			const providerId = this.dataSelected.id;
			const id = this.currentId;
			this.deleteAssociateProvider({ id, providerId });
			this.onClose();
		},
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getFinancialVehicleProviders({
				id: this.currentId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		updateHeader(): void {
			const dataHeader = {
				title: this.financialVehicleHeaderData.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					this.financialVehicleHeaderData.content.id,
					this.financialVehicleHeaderData.content.type,
					[null, this.financialVehicleHeaderData.content.document],
				],
			};
			this.$emit('onUpdatedHeader', dataHeader);
		},
		updatedList(): void {
			this.dataList = this.financialVehicleProviderList.content;
		},
		updatedTotalPages(): void {
			this.totalPages = this.financialVehicleProviderList.totalPages;
		},
		updatedDateFooter(): void {
			const updatedFooterFormData = format(this.financialVehicleProviderList.meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},
		handleFinalizeAssociation(data) {
			this.showModal = true;
			this.dataSelected = data;
		},
		handleReAssociateOption(data) {
			this.dataSelected = data;
			this.$dialog
				.confirm(
					{
						body: `Deseja associar novamente o fornecedor <b>${stripTags(
							data.name
						)}</b>? `,
						title: 'Associar Novamente',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const providerId = this.dataSelected.id;
					const id = this.currentId;
					this.reAssociateProvider({ id, providerId });
				});
		},
	},
	watch: {
		financialVehicleProviderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedTotalPages();
				this.updatedDateFooter();
			}
		},
		financialVehicleHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updateHeader();
			}
		},
		deleteAssociateProviderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					`Associação finalizada com sucesso para o fornecedor <b>${this.dataSelected.name}</b>!`
				);
				this.reloadPage();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível finalizada a associação deste fornecedor <b>${this.dataSelected.name}</b>.`
				);
			}
		},
		reAssociateProviderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					`Associação ao veículo financeiro realizada com sucesso para o fornecedor <b>${this.dataSelected.name}</b>!`
				);
				this.reloadPage();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível associar ao veículo financeiro <b>${this.dataSelected.name}</b>.`
				);
			}
		},
	},
});
</script>
