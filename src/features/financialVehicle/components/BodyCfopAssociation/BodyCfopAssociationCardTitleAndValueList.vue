<template>
	<farm-row>
		<farm-col
			v-for="(item, i) in data"
			:key="i"
			:cols="12"
			:md="getColsSize(i)"
			:style="{
				borderRight: i !== data.length ? '1px solid #ddd' : 'none',
			}"
		>
			<p class="mb-2 custom-title">{{ item.title }}</p>
			<p class="custom-subtitle font-weight-medium mb-0">
				{{ formatValueOrNA(item.value) }}
			</p>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { formatValueOrNA } from '@/helpers/formatCards';
import { defineComponent } from 'vue';

type CfopItem = {
	title: string;
	value: string;
};

export default defineComponent({
	props: {
		data: {
			type: Array as () => CfopItem[],
			required: true,
			default: () => [],
		},
	},

	setup() {
		function getColsSize(index: number): number {
			const sizes = [1, 5, 2, 2, 2];
			return sizes[index] ?? 0;
		}

		return {
			getColsSize,
			formatValueOrNA,
		};
	},
});
</script>

<style lang="scss">
.custom-title {
	color: #858585;
}

.custom-subtitle {
	font-size: 0.85rem;
}
</style>
