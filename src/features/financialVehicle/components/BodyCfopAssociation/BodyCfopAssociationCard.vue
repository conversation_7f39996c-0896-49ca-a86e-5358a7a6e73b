<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="10">
					<CfopCardTitleAndValueList :data="cardHeaderList" />
				</farm-col>
				<farm-col cols="2" align="end" class="card-action justify-end flex">
					<farm-typography
						size="sm"
						class="mb-2 d-flex justify-center"
						weight="600"
						color="gray"
						colorVariation="darken"
						>Liberar CFOP</farm-typography
					>
					<p
						class="
							custom-subtitle
							font-weight-medium
							mb-0
							d-flex
							align-center
							justify-center
						"
					>
						<farm-switcher v-model="isActive" class="justify-self-end" block />
						<span class="ml-3">{{ isActive ? 'Sim' : 'Não' }}</span>
					</p>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row y-grid-gutters>
				<farm-col cols="12">
					<CardTextBody label="Descrição" :value="data.description" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import CfopCardTitleAndValueList from './BodyCfopAssociationCardTitleAndValueList.vue';
import { watch } from 'vue';
import { FinancialVehicleCfop } from '@/services/cfop';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
		CfopCardTitleAndValueList,
	},
	props: {
		data: {
			type: Object as () => FinancialVehicleCfop,
			required: true,
		},
	},
	setup(props, { emit }) {
		const isAssociated = props.data.associated === '1';
		const isActive = ref(isAssociated);
		const cardHeaderList = ref([
			{
				title: 'Código',
				value: props.data.code,
			},
			{
				title: 'Nome',
				value: props.data.name || 'Venda de mercadoria adquirida ou recebida de terceiros',
			},
			{
				title: 'Origem/Destino',
				value: props.data.sameState ? 'Mesmo Estado' : 'Outro Estado',
			},
			{
				title: 'Amostra/Bonificação',
				value: props.data.sample ? 'Sim' : 'Não',
			},
			{
				title: 'Venda Performada?',
				value: props.data.salesPerformed ? 'Sim' : 'Não',
			},
		]);

		watch(isActive, () => {
			emit('update-status', {
				cfopCodeId: +props.data.id,
				permitedByVehicle: isActive.value ? 1 : 0,
			});
		});

		return {
			isActive,
			cardHeaderList,
			formatValueOrNA,
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
			formatConcentrationPercentage,
		};
	},
});
</script>

<style lang="scss">
.custom-title {
	color: rgba(92, 92, 92, 1);
}

.custom-subtitle {
	font-size: 0.85rem;
}
</style>
