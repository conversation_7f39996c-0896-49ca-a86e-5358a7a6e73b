<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="formatValueOrNA(data.name)" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndType" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense />
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row >
				<farm-col cols="4">
					<CardTextBody label="Cessão" :value="formatYesOrNo(data.assignment)" />
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Emissão de Lastro"
						:value="formatYesOrNo(data.emissionOfBallast)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody label="Originação" :value="formatYesOrNo(data.origination)" />
				</farm-col>
			</farm-row>
			<farm-row y-grid-gutters>
				<farm-col cols="4">
					<CardTextBody
						label="Percentual de Concentração"
						:value="formatConcentrationPercentage(data.concentrationPercentage)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Fim de Associação"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatValueOrNA,
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
			formatConcentrationPercentage,
			listIdAndType: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{ label: 'Tipo', value: this.data.type, copyText: '' },
				
			],
		};
	},
});
</script>
