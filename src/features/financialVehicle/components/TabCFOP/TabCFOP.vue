<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12">
				<div class="d-flex">
					<span class="mr-3">
						<farm-icon-box
							icon="clipboard-text-outline"
							color="secondary-golden"
							variation="darken"
							size="md"
						/>
					</span>
					<div>
						<farm-typography size="lg" weight="500">
							Listagem de CFOPs
						</farm-typography>
						<div class="mt-1">
							<farm-typography size="sm">
								<span>Visualize e configure os CFOPs</span>
								<farm-tooltip class="mx-1 w-auto">
									<p>
										<b>CFOP</b>
									</p>
									<p>Código Fiscal de Operações e de Prestações.</p>
									<template #activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
								<span>permitidos neste Veículo Financeiro.</span>
							</farm-typography>
						</div>
					</div>
				</div>
			</farm-col>
		</farm-row>

		<farm-row class="mt-6">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar CFOP"
					tooltip="Realize sua busca pelo código do CFOP"
					:showFilters="showFilters"
					@onInputChange="handleSearchInput"
					@onClick="toggleFilters"
				/>
			</farm-col>

			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					class="mt-8"
					customIcon="plus"
					title="Associar e Alterar CFOP"
					:to="`/admin/cadastros/veiculos_financeiros/${currentId}/associacao_cfop/novo`"
					:icon="true"
				>
					Associar e Alterar CFOP
				</farm-btn-confirm>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<CfopsFilter v-show="showFilters" key="filters" @applyFilters="applyFilters" />
		</collapse-transition>

		<farm-row justify="end">
			<farm-col cols="12" md="3" align="end">
				<farm-select
					item-text="label"
					item-value="value"
					v-model="selectedSort"
					:items="sortOptions"
					@change="changeSort"
				></farm-select>
			</farm-col>
		</farm-row>

		<farm-box class="px-2 mb-6">
			<farm-row extra-decrease v-if="cfopsList.length">
				<farm-col cols="12" md="12">
					<CfopCard
						v-for="item in cfopsList"
						:data="item"
						:key="item.id"
						class="mb-6"
						@remove-cfop="promptRemoveCfop"
					/>
				</farm-col>
			</farm-row>

			<farm-row extra-decrease v-else>
				<farm-col>
					<farm-emptywrapper subtitle="Tente associar um CFOP." />
				</farm-col>
			</farm-row>
		</farm-box>

		<farm-prompt-user
			v-model="showRemoveCfopPrompt"
			title="Remover CFOP"
			subtitle=""
			match="REMOVER"
			confirmLabel="Sim"
			closeLabel="Cancelar"
			@onConfirm="confirmPrompt"
		>
			<template #subtitle>
				<farm-typography size="sm">
					Deseja realmente remover a associação com o CFOP
					<span class="font-weight-bold"> {{ cfopToRemoveCode }}?</span>
				</farm-typography>

				<farm-typography size="sm" class="mt-3">
					Essa modificação pode impactar em outras jornadas na plataforma.
				</farm-typography>

				<farm-typography size="sm" class="mt-3 mb-6">
					Escreva no campo abaixo
					<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography>
					para confirmar a remoção do CFOP.
				</farm-typography>
			</template>
		</farm-prompt-user>

		<farm-row extra-decrease v-if="cfopsList.length">
			<farm-box>
				<farm-datatable-paginator
					:page="page"
					:totalPages="pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import CfopCard from './components/CfopCard.vue';
import { listCFOPSort } from '../../configurations/sorts/sorts';
import store from '@/store';
import { format } from '@/helpers/formatUpdateUser';
import { usePageable } from '@/composibles/usePageable/usePageable';

import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import CfopsFilter from './components/CfopFilter.vue';
import type {
	FetchFinancialVehicleCFOPSQuery,
	FinancialVehicleCfop,
	FinancialVehicleCfopsResponse,
} from '@/services/cfop';
import {
	useFetchFinancialVehicleCfops,
	usePatchFinancialVehicleCfops,
} from '@/features/_composables';

type SortOrder = FetchFinancialVehicleCFOPSQuery['order'];

export default {
	components: {
		CfopsFilter,
		CfopCard,
	},
	props: {
		currentId: {
			type: Number,
			required: true,
		},
	},
	setup({ currentId }, { emit }) {
		const {
			state: fetchCfopsState,
			data,
			fethCfops,
			error: cfopError,
		} = useFetchFinancialVehicleCfops();
		const { state: patchCfopState, patchCfops } = usePatchFinancialVehicleCfops();
		const selectedSort = ref<SortOrder>('ASC');
		const sortOptions = ref(listCFOPSort);
		const showFilters = ref(false);
		const showRemoveCfopPrompt = ref(false);
		const cfopsData = ref<FinancialVehicleCfopsResponse>(null);
		const cfopsList = ref<FinancialVehicleCfop[]>([]);
		const cfopToRemoveId = ref<number | null>(null);
		const cfopToRemoveCode = ref<string>('');

		const initialPagination = {
			pageNumber: 0,
			pageSize: 10,
			sort: null,
			totalElements: 0,
			totalPages: 0,
		};

		const {
			page,
			pagination,
			isFilterCounter,
			onChangePageLimit,
			onChangePage,
			onSortSelect,
			onApplyFilter,
			filterCurrent,
		} = usePageable(
			{
				calbackFn: async filters => {
					const params: FetchFinancialVehicleCFOPSQuery = {
						...filters,
						onlyAssociated: 1,
						orderby: 'code',
						order: selectedSort.value,
					};

					if (filters.search) {
						params.search = filters.search;
					}

					await fethCfops(currentId, params);

					if (cfopError.value) {
						cfopsData.value = null;
						pagination.value.totalPages = 0;
						cfopsList.value = [];
					} else {
						cfopsData.value = data.value;
						pagination.value.totalPages = data.value.totalPages;
						pagination.value.totalElements = data.value.totalItems;
						cfopsList.value = data.value.content;
					}
				},
				keyInputSearch: 'search',
				filters: {
					salesPerformed: null,
					search: '',
				},
				lowercaseSort: true,
				sort: {
					order: 'ASC',
					orderby: 'code',
				},
			},
			initialPagination
		);

		const financialVehicleHeaderData = computed(
			() => store.getters['cadastros/financialVehicleHeaderData']
		);
		const financialVehicleHeaderRequestStatus = computed(
			() => store.getters['cadastros/financialVehicleHeaderRequestStatus']
		);
		const financialVehicleByIdData = computed(
			() => store.getters['cadastros/financialVehicleById']
		);

		function changeSort(sort: SortOrder) {
			selectedSort.value = sort;
			onSortSelect(`${sort}_code`);
		}

		function toggleFilters() {
			showFilters.value = !showFilters.value;
		}

		async function promptRemoveCfop({ id, code }: { id: number; code: string }) {
			cfopToRemoveId.value = id;
			cfopToRemoveCode.value = code;
			showRemoveCfopPrompt.value = true;
		}

		async function confirmPrompt() {
			await patchCfops(currentId, [
				{
					cfopCodeId: cfopToRemoveId.value,
					permitedByVehicle: 0,
				},
			]);

			notification(RequestStatusEnum.SUCCESS, `CFOP removido com sucesso!`);

			onChangePage(1);
		}

		function updateHeader() {
			const dataHeader = {
				title: financialVehicleHeaderData.value.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					financialVehicleHeaderData.value.content.id,
					financialVehicleHeaderData.value.content.type,
					[null, financialVehicleHeaderData.value.content.document],
				],
			};
			emit('onUpdatedHeader', dataHeader);
		}

		function updatedDateFooter() {
			const updatedFooterFormData = format(data.value?.meta);
			emit('onUpdateFooterFormData', updatedFooterFormData);
		}

		function applyFilters(queryFilters: FetchFinancialVehicleCFOPSQuery) {
			onApplyFilter({
				...filterCurrent.value,
				...queryFilters,
				page: 0,
				limit: pagination.value.pageSize,
			});
		}

		function handleSearchInput(value: string) {
			onApplyFilter({
				...filterCurrent.value,
				search: value,
				page: 0,
			});
		}

		watch(showRemoveCfopPrompt, async value => {
			if (!value) {
				cfopToRemoveId.value = null;
				cfopToRemoveCode.value = '';
			}
		});

		watch(financialVehicleHeaderRequestStatus, () => {
			if (financialVehicleHeaderRequestStatus.value === 'SUCCESS') {
				updateHeader();
			}
		});

		watch(fetchCfopsState, () => {
			if (fetchCfopsState.value === 'SUCCESS') {
				updatedDateFooter();
			}
		});

		onMounted(async () => {
			onChangePage(1);
		});

		return {
			cfopError,
			financialVehicleByIdData,
			confirmPrompt,
			changeSort,
			handleSearchInput,
			toggleFilters,
			onChangePage,
			onChangePageLimit,
			promptRemoveCfop,
			cfopToRemoveId,
			cfopToRemoveCode,
			showRemoveCfopPrompt,
			page,
			pagination,
			fetchCfopsState,
			patchCfopState,
			cfopsData,
			cfopsList,
			selectedSort,
			showFilters,
			sortOptions,
			applyFilters,
			isFilterCounter,
		};
	},
};
</script>

<style lang="scss" scoped>
::v-deep {
	.remove-copy-text .farm-tooltip {
		display: none;
	}
}
</style>
