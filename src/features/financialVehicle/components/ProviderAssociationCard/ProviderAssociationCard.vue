<template>
	<Cards>
		<template slot="header">
			<farm-row align="center">
				<farm-col cols="12">
					<CardCheckboxHeader>
						<template slot="checkbox">
							<farm-checkbox
								class="mr-3"
								v-model="isChecked"
								size="sm"
								ref="checkbox"
								:value="true"
							/>
						</template>
						<template slot="title">
							<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
							<CardListTextHeader noSpacing :data="listIdAndRaiz" />
						</template>
						<template slot="status">
							<StatusActiveAndInactive :status="data.status" dense />
						</template>
					</CardCheckboxHeader>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody
						label="Data de Cadastro"
						:value="formatDateOrNA(data.createdAt)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardCheckboxHeader from '@/components/CardCheckboxHeader';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';
import { formatDateOrNA } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardCheckboxHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatDateOrNA,
			isChecked: false,
			listIdAndRaiz: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{
					label: 'Raiz',
					value: this.data.raiz,
					copyText: this.data.raiz,
					successMessage: 'Raiz copiado para área de transferência!',
				},
			],
		};
	},
	methods: {
		updatedCheckBox(): void {
			this.$refs.checkbox.reset();
		},
	},
	watch: {
		isChecked(newValue): void {
			const type = newValue ? 'add' : 'remove';
			this.$emit('updateItem', {
				data: this.data,
				type,
			});
		},
		data(): void {
			this.updatedCheckBox();
		},
	},
});
</script>
