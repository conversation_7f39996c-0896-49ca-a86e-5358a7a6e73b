<template>
	<farm-container>
		<HeaderForm :data="dataHeaderForm" withLine />
		<TitlePageForm label="Associar Fornecedores" noPipe />
		<farm-row v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				>
					<LabelFilterProviderAssociation
						label="Buscar Fornecedor"
						:dataSelected="itemSelected"
					/>
				</farm-form-mainfilter>
			</farm-col>
			<farm-col cols="12" md="6" align="right">
				<farm-btn
					outlined
					class="ml-4 mt-8"
					title="Desmarcar Selecionados"
					v-if="itemSelected.length > 0"
					@click="resetItemSelected"
				>
					Desmarcar Selecionados
				</farm-btn>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<ProviderFilter key="filters" v-show="filter" @onApply="searchListener" />
		</collapse-transition>
		<farm-row justify="end" v-if="!isError">
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<ProviderAssociationList
			v-if="!isError"
			:data="dataList"
			@updatedItemSeleted="updatedItemSeleted"
		/>
		<farm-row extra-decrease v-if="!isError">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-col cols="12" v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</farm-col>
		<farm-loader mode="overlay" v-if="isLoading" />
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';

import { RequestStatusEnum, pageable, notification } from '@farm-investimentos/front-mfe-libs-ts';
import TitlePageForm from '@/components/TitlePageForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import { headersPages } from '../../configurations/headersPages';
import { providerAssociationSortOptions } from '../../configurations/sorts';

import ProviderAssociationList from '../ProviderAssociationList';
import ProviderFilter from '../ProviderFilter';
import LabelFilterProviderAssociation from '../LabelFilterProviderAssociation';

export default defineComponent({
	components: {
		ProviderAssociationList,
		ProviderFilter,
		HeaderForm,
		FooterForm,
		TitlePageForm,
		LabelFilterProviderAssociation,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'createdAt_DESC',
			lastSearchFilters: { page: 0, limit: 10 },
			filterInputKey: 'search',
			sortOptions: providerAssociationSortOptions,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			dataList: [],
			itemSelected: [],
			totalPages: 0,
			dataSelected: null,
			showModal: false,
			filter: false,
			disabledButtonFooter: null,
			labelButton: 'Associar',
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
		};
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehicleNotRelatedProvidersList: 'financialVehicleNotRelatedProvidersList',
			financialVehicleNotRelatedProvidersRequestStatus:
				'financialVehicleNotRelatedProvidersRequestStatus',
			financialVehicleHeaderRequestStatus: 'financialVehicleHeaderRequestStatus',
			financialVehicleHeaderData: 'financialVehicleHeaderData',
			associateUnrelatedProviderRequestStatus: 'associateUnrelatedProviderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatuses = [
				this.financialVehicleNotRelatedProvidersRequestStatus,
				this.financialVehicleHeaderRequestStatus,
				this.associateUnrelatedProviderRequestStatus,
			];
			return requestStatuses.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatuses = [
				this.financialVehicleNotRelatedProvidersRequestStatus.type,
				this.financialVehicleHeaderRequestStatus.type,
				this.associateUnrelatedProviderRequestStatus.type,
			];
			return requestStatuses.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	mounted(): void {
		this.getFinancialVehicleHeaders({
			financialVehicleId: this.currentId,
		});
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicleNotRelatedProviders: 'getFinancialVehicleNotRelatedProviders',
			getFinancialVehicleHeaders: 'getFinancialVehicleHeaders',
			associateUnrelatedProvider: 'associateUnrelatedProvider',
		}),
		reload(): void {
			this.doSearch();
		},
		onClose(): void {
			this.showModal = false;
		},
		onCancel() {
			this.$router.push({
				path: `/admin/cadastros/veiculos_financeiros/${this.currentId}/editar?path=fornecedores`,
			});
		},
		onSave() {
			const payload = {
				providerIds: this.itemSelected,
			};
			this.associateUnrelatedProvider({
				id: this.currentId,
				payload,
			});
		},
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };

			this.getFinancialVehicleNotRelatedProviders({
				id: this.currentId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		updatedList(): void {
			this.dataList = [...this.financialVehicleNotRelatedProvidersList.content];
			this.totalPages = this.financialVehicleNotRelatedProvidersList.totalPages;
		},
		updatedItemSeleted(item): void {
			if (item.type === 'add') {
				this.itemSelected.push(item.data.id);
			} else if (item.type === 'remove') {
				const index = this.itemSelected.indexOf(item.data.id);
				this.itemSelected.splice(index, 1);
			}
		},
		resetItemSelected(): void {
			this.dataList = this.dataList.map(item => {
				return {
					...item,
					checked: false,
				};
			});
			this.itemSelected = [];
		},
		createMessageSuccess(): string {
			const msg = 'Associação ao veículo financeiro realizada com sucesso para';
			const count = this.itemSelected.length;

			return this.$tc('providers.provider', count, { msg, count });
		},
		updateHeader(): void {
			const dataHeader = {
				title: this.financialVehicleHeaderData.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					this.financialVehicleHeaderData.content.id,
					this.financialVehicleHeaderData.content.type,
					[null, this.financialVehicleHeaderData.content.document],
				],
			};
			this.onUpdatedHeader(dataHeader);
		},
		onUpdatedHeader(data): void {
			this.dataHeaderForm = parseDataHeader(data, headersPages);
		},
		redirect(): void {
			setTimeout(() => {
				this.onCancel();
			}, 2000);
		},
	},
	watch: {
		financialVehicleNotRelatedProvidersRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
			}
		},
		itemSelected(newValue) {
			this.disabledButtonFooter = newValue.length > 0;
		},
		financialVehicleHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updateHeader();
			}
		},
		associateUnrelatedProviderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(RequestStatusEnum.SUCCESS, this.createMessageSuccess());
				this.redirect();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, 'Não foi possivel fazer a associação.');
			}
		},
	},
});
</script>
