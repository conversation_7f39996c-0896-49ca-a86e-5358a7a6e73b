export type FinancialVehicleTabDataFormTypes = {
	type: string | number;
	name: string;
	startDate: string | null;
	endDate: string | null;
};

export type FinancialVehicleDataTypes = FinancialVehicleTabDataFormTypes & {
	id: number;
};

export type FinancialVehicleDataByIdTypes = FinancialVehicleTabDataFormTypes & {
	id?: number;
	financialVehicleTypeId: number;
};

export type FinancialVehicleTypeDataTypes = {
	id: number;
	type: string;
};
