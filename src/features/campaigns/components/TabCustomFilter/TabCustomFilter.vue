<template>
	<farm-box class="tab-custom-filter">
		<farm-row justify="space-between">
			<farm-col cols="3" class="filter-sidebar">
				<farm-bodytext type="1" variation="bold" color="primary" class="mb-4">
					Campanhas filtradas:
				</farm-bodytext>
			
				<farm-col no-gutters>
					<div
						v-for="campaign in campaigns"
						:key="campaign.id"
						class="campaign-card mb-6"
					
					>
						<farm-row justify="space-between" align="center" noDefaultGutters>
								<farm-typography size="md" weight="500">
									{{ campaign.name }}
								</farm-typography>
								
								<farm-switcher
									v-model="campaign.isActive"
									@input="onToggleCampaign(campaign.id, $event)"
								/>
						
						</farm-row>
					</div>
				</farm-col>
			</farm-col>
			<farm-col cols="9">
				<farm-form class="mb-6">
					<farm-row>
						<farm-col cols="12">
							<farm-text class="mb-4">Preencha os campos abaixo para popular a timeline:</farm-text>
						</farm-col>
					</farm-row>
					
					<farm-row>
						<farm-col cols="12" md="4">
							<farm-label>Produto Comercial *</farm-label>
							<farm-select
								v-model="formFilters.commercial_prd_id"
								:items="commercialProductsForSelect"
								item-text="text"
								item-value="value"
								placeholder="Selecione um produto"
								required
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label>Tipo de Operação *</farm-label>
							<farm-select
								v-model="formFilters.operation_type"
								:items="operationTypesForSelect"
								item-text="text"
								item-value="value"
								placeholder="Selecione o tipo"
								required
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label>Status da Campanha *</farm-label>
							<farm-select
								v-model="formFilters.campaign_status"
								:items="FORM_STATUSES"
								item-text="text"
								item-value="value"
								placeholder="Selecione o status"
								multiple
								required
							/>
						</farm-col>
					</farm-row>
					
					<farm-row class="mt-4">
						<farm-col cols="12" class="d-flex justify-end">
							<farm-btn class="mr-2" outlined @click="limparFiltros">Limpar Filtros</farm-btn>
							<farm-btn @click="aplicarFiltros" :disabled="!isFormValid">Aplicar Filtros</farm-btn>
						</farm-col>
					</farm-row>
				</farm-form>
				
				<!-- Gantt Chart Timeline -->
				<div class="timeline-container">
					<div v-if="ganttChartData && ganttChartData.groups.length > 0" class="gantt-wrapper">
						<farm-gantt-chart :data="ganttChartData" />
					</div>
					<div v-else-if="campaignTimelineRequestStatus === 'SUCCESS'" class="no-data">
						Nenhum dado encontrado para os filtros selecionados.
					</div>
				</div>
			</farm-col>
		</farm-row>
		
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onBeforeMount, computed } from 'vue';
import { Switcher } from '@farm-investimentos/front-mfe-components/src/components/Switcher';
import { GanttChart } from '@farm-investimentos/front-mfe-components/src/components/GanttChart';
import { useIsLoading } from '@/composibles';
import { OPERATION_TYPES } from '@/constants';
import { useCampaign } from '../../composables';
import { FORM_STATUSES } from '../../constants';

interface CampaignData {
	id: number;
	name: string;
	isActive: boolean;
}

export default defineComponent({
	name: 'TabCustomFilter',
	components: {
		'farm-switcher': Switcher,
		'farm-gantt-chart': GanttChart
	},
	setup() {
		const {
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsListRequestStatus,
			fetchCommercialProducts,
			fetchCampaignTimeline,
		} = useCampaign();


		const campaigns = ref<CampaignData[]>([
			{ id: 1, name: 'Campanha Black Friday', isActive: true },
			{ id: 2, name: 'Campanha Natal 2024', isActive: false },
			{ id: 3, name: 'Campanha Ano Novo', isActive: true },
			{ id: 4, name: 'Campanha Carnaval', isActive: false }
		]);

		const formFilters = ref({
			commercial_prd_id: null,
			operation_type: null,
			campaign_status: [],
		});

		const isLoading = useIsLoading([
			campaignTimelineRequestStatus,
			commercialProductsListRequestStatus,
		]);


		const commercialProductsForSelect = computed(() =>
			commercialProductsList.value
				?.filter(product => product.enabled === 1)
				?.map(({ id, name }) => ({
					value: id,
					text: name
				})) || []
		);


		const operationTypesForSelect = computed(() =>
			OPERATION_TYPES.map(({ id, name }) => ({
				value: id,
				text: name
			}))
		);


		const isFormValid = computed(() => {
			return formFilters.value.commercial_prd_id !== null &&
			formFilters.value.operation_type !== null &&
			formFilters.value.campaign_status.length > 0;
		});

		// Transform API data to GanttChart format
		const ganttChartData = computed(() => {
			if (!campaignTimeline.value || campaignTimeline.value.length === 0) {
				return { groups: [] };
			}

			const groups = campaignTimeline.value.map(item => {
				const { campaign, commercialProduct } = item;

				return {
					title: campaign.name,
					bars: [
						{
							id: `campaign-${campaign.id}`,
							label: 'Vigência da Campanha',
							start: new Date(campaign.startDate),
							end: new Date(campaign.endDate),
							color: '#7BC4F7',
							tooltipData: {
								'Taxa': `${campaign.tax.toFixed(2)}%`,
								'Status': campaign.status ? 'Ativo' : 'Inativo',
								'Período': `${campaign.startDate} a ${campaign.endDate}`
							}
						},
						{
							id: `product-${commercialProduct.id}`,
							label: 'Vigência do Produto Comercial',
							start: new Date(commercialProduct.startDate),
							end: new Date(commercialProduct.endDate),
							color: '#8BB455',
							tooltipData: {
								'Produto': commercialProduct.name,
								'Status': commercialProduct.status ? 'Ativo' : 'Inativo',
								'Período': `${commercialProduct.startDate} a ${commercialProduct.endDate}`
							}
						},
						{
							id: `disbursement-${commercialProduct.id}`,
							label: 'Período de Desembolso',
							start: new Date(commercialProduct.startDisbursementDate),
							end: new Date(commercialProduct.endDisbursementDate),
							color: '#FFB84D',
							tooltipData: {
								'Produto': commercialProduct.name,
								'Período': `${commercialProduct.startDisbursementDate} a ${commercialProduct.endDisbursementDate}`
							}
						},
						{
							id: `due-${commercialProduct.id}`,
							label: 'Intervalo Vencimento',
							start: new Date(commercialProduct.startDueDate),
							end: new Date(commercialProduct.endDueDate),
							color: '#F7857F',
							tooltipData: {
								'Produto': commercialProduct.name,
								'Período': `${commercialProduct.startDueDate} a ${commercialProduct.endDueDate}`
							}
						}
					]
				};
			});

			return { groups };
		});

		const onToggleCampaign = (campaignId: number, value: boolean) => {
			const campaign = campaigns.value.find(c => c.id === campaignId);
			if (campaign) {
				campaign.isActive = value;
			}
		};

		const aplicarFiltros = () => {
			const query = {
				commercial_prd_id: formFilters.value.commercial_prd_id,
				operation_type: formFilters.value.operation_type,
				campaign_status: formFilters.value.campaign_status,
			};

			fetchCampaignTimeline({ query });
		};

		const limparFiltros = () => {
			formFilters.value = {
				commercial_prd_id: null,
				operation_type: null,
				campaign_status: [],
			};
		};


		onBeforeMount(() => {
			fetchCommercialProducts();
		});

		return {
			campaigns,
			formFilters,
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsForSelect,
			operationTypesForSelect,
			FORM_STATUSES,
			isFormValid,
			isLoading,
			ganttChartData,
			onToggleCampaign,
			aplicarFiltros,
			limparFiltros,
		};
	}
});
</script>

<style lang="scss" scoped>
.tab-custom-filter {
	position: relative;
	display: flex;
	flex-direction: column;
}

.tab-custom-filter :deep(.farm-row) {
	flex: 1;
	display: flex;
	align-items: stretch;
}

.filter-sidebar {
	background-color: var(--farm-primary-lighten);
	padding: 24px;
	margin: -24px -12px -24px -12px; /* Compensa todos os paddings do farm-box com valor maior */
	padding-right: 12px; /* Restaura o espaçamento à direita */
	display: flex;
	flex-direction: column;
}

.filter-title {
	color: var(--farm-primary-darken);
	font-weight: bold;
}

.timeline-container {
	min-height: 200px;
}

.gantt-wrapper {
	height: 600px;
	width: 100%;

	// Fix z-index issues with dropdowns/selects
	:deep(.farm-gantt-chart__tooltip-container) {
		z-index: 1050 !important; // Higher than typical dropdown z-index (1000)
	}

	// Ensure the gantt chart doesn't interfere with form controls
	:deep(.farm-gantt-chart) {
		position: relative;
		z-index: 1;
	}
}

.campaign-card {
	background-color: #ECF6DC;
	border-radius: 5px;
	padding: 16px;
	margin-bottom: 8px;
	width: 100%;
}

.timeline-item {
	background-color: #f5f5f5;
	border-radius: 8px;
	padding: 16px;
	margin-bottom: 12px;
	border-left: 4px solid var(--farm-primary);
}

.no-data {
	text-align: center;
	color: #666;
	padding: 40px 0;
}

</style>

/*


*/