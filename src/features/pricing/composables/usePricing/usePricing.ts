import { computed, ref } from 'vue';
import { useGetter, useIsLoading, useStore } from '@/composibles';
import { RequestQueryString } from '@/types';

export function usePricing() {
	const store = useStore();
	const pricingSearchTerm = ref('');

	const pricings = computed(useGetter('pricing', 'pricings'));
	const hasPricings = computed(() => pricings.value?.length);
	const fetchPricingsRequestStatus = computed(useGetter('pricing', 'fetchPricingsRequestStatus'));

	const handleSearchPricing = (searchTerm: string): void => {
		pricingSearchTerm.value = searchTerm;

		const params = new URLSearchParams({
			product_name: pricingSearchTerm.value,
		});

		fetchPricings(searchTerm ? params : '');
	};
	const fetchPricings = (params?: RequestQueryString) => {
		store.dispatch('pricing/fetchPricings', { params });
	};
	const resetPricingList = (callback?: Function) => {
		store.commit('pricing/setPricings', []);

		fetchPricings();

		if (typeof callback === 'function') callback();
	};

	const isLoading = useIsLoading([fetchPricingsRequestStatus]);

	return {
		pricings,
		hasPricings,
		isLoading,
		pricingSearchTerm,
		fetchPricings,
		handleSearchPricing,
		resetPricingList,
	};
}
