import { ref, watch, computed, ComputedRef } from 'vue';

export interface IPageableRequest {
	page: number;
	limit: number;
	order: string;
	orderby: string;
}

export interface IPageableResponse {
	pageNumber: number;
	pageSize: number;
	sort: string | null;
	totalElements: number;
	totalPages: number;
}

export function usePageable(
	callback: Function,
	pageable: ComputedRef<IPageableResponse> | null,
	defaultFilter?: any
) {
	const canDoSearch = ref(true);
	const defaultPlatformFilter = {
		orderby: 'createdAt',
	};

	const filters = ref({
		pageNumber: 0,
		pageSize: 10,
		sort: null,
		totalElements: 0,
		totalPages: 0,
	} as IPageableResponse);

	const presentableFilterValues = computed(() => ({
		pageNumber: filters.value.pageNumber + 1,
	}));

	const setPageableFilters = (newFilters: IPageableResponse) => {
		filters.value = newFilters;
	};

	const onChangePageLimit = (newPageLimit: number) => {
		filters.value.pageSize = newPageLimit;
		filters.value.pageNumber = 0;
		if (canDoSearch) callback(transformIntoQueryParams());
	};

	const onChangePage = (newPage: number) => {
		filters.value.pageNumber = newPage - 1;

		const params = transformIntoQueryParams();

		if (canDoSearch.value) callback(params);
	};

	const transformIntoQueryParams = () => {
		const initialFilter = defaultFilter || defaultPlatformFilter;

		const dto = {
			pageNumber: 'page',
			pageSize: 'limit',
		};

		const DTOReducer = (accumulator, [key, value]) => {
			if (Object.keys(dto).includes(key)) {
				accumulator[dto[key]] = value;
			}

			return accumulator;
		};

		const requestPageableParameters = Object.entries(filters.value).reduce(
			DTOReducer,
			initialFilter
		);

		return new URLSearchParams(requestPageableParameters);
	};

	const unwatch = watch(pageable, newPageable => {
		setPageableFilters(newPageable);

		unwatch();
	});

	return {
		canDoSearch,
		filters,
		presentableFilterValues,
		transformIntoQueryParams,
		setPageableFilters,
		onChangePageLimit,
		onChangePage,
	};
}
