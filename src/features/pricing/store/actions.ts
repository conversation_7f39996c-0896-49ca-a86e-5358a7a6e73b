import { RequestQueryString } from '@/types';
import {
	downloadModelPricing as downloadModelPricingService,
	uploadPricingSpreadsheet as uploadPricingSpreadsheetService,
	fetchPricings as fetchPricingsService,
	fetchPricingHistory as fetchPricingHistoryService,
	exportPricingSpreadsheet as exportPricingSpreadsheetService,
} from './../services';
import {
	RequestStatusEnum,
	StatusEnum,
	errorBuilder,
	notification,
} from '@farm-investimentos/front-mfe-libs-ts';
import { fetchPricingHistoryParameters } from '../types';

export default {
	async downloadModelPricing({ commit }) {
		commit('setDownloadPricingRequestStatus', RequestStatusEnum.START);
		try {
			await downloadModelPricingService();
			commit('setDownloadPricingRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setDownloadPricingRequestStatus', errorBuilder(error));
		}
	},

	async exportPricingSpreadsheet({ commit }) {
		commit('setExportPricingSpreadsheetRequestStatus', RequestStatusEnum.START);
		try {
			await exportPricingSpreadsheetService();

			commit('setExportPricingSpreadsheetRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setExportPricingSpreadsheetRequestStatus', errorBuilder(error));
		}
	},

	async uploadPricingSpreadsheet(
		{ commit },
		{ payload, params }: { payload: FormData; params?: RequestQueryString }
	) {
		commit('setUploadPricingSpreadsheetRequestStatus', RequestStatusEnum.START);

		try {
			const response = await uploadPricingSpreadsheetService(payload, params);

			if (response.status === 201) {
				commit('setUploadPricingSpreadsheetRequestStatus', RequestStatusEnum.SUCCESS);
				notification(StatusEnum.SUCCESS, 'Importado com sucesso');
				return;
			}

			commit('setUploadPricingSpreadsheetRequestStatus', {
				type: RequestStatusEnum.ERROR,
				httpStatus: response.status,
				message: response.data,
			});
		} catch (error) {
			const {
				response: {
					data: { data: content },
				},
			} = error;

			commit('setUploadPricingSpreadsheetRequestStatus', {
				type: RequestStatusEnum.ERROR,
				httpStatus: error.response.status,
				message: content.errors[0],
			});
		}
	},

	async fetchPricings({ commit }, { params }: { params?: RequestQueryString } | null) {
		commit('setFetchPricingsRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchPricingsService(params);

			commit('setPricings', data.data.content ?? []);
			commit('setFetchPricingsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setFetchPricingsRequestStatus', errorBuilder(error));
		}
	},

	async fetchPricingHistory({ commit }, { payload, params }: fetchPricingHistoryParameters) {
		commit('setFetchPricingHistoryRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchPricingHistoryService(payload, params);

			commit('setPricingHistoryList', data?.data?.content ?? []);
			commit('setPricingHistoryPageable', data?.data?.pageable ?? []);
			commit('setFetchPricingHistoryRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setFetchPricingHistoryRequestStatus', errorBuilder(error));
		}
	},
};
