<template>
	<farm-card class="mb-6">
		<farm-card-content>
			<farm-row justify="space-between" no-default-gutters>
				<farm-bodytext variation="bold">{{ pricing.periods[0].productName }}</farm-bodytext>
				<farm-btn
					title="Ver histórico"
					aria-label="Ver Histórico"
					icon
					@click="$emit('toggle-history-modal', productFinancialVehicle)"
				>
					<farm-icon role="presentation">open-in-new</farm-icon>
				</farm-btn>
			</farm-row>

			<farm-row no-default-gutters>
				<farm-caption
					color="gray"
					size="md"
					class="pr-2 d-flex align-center justify-center farm-caption__bordered"
				>
					ID: {{ pricing.financialVehicleId }}
				</farm-caption>
				<farm-caption
					color="gray"
					size="md"
					class="pl-2 d-flex align-center justify-center"
				>
					Veículo Financeiro: {{ pricing.financialVehicle }}
				</farm-caption>
			</farm-row>
		</farm-card-content>

		<farm-line no-spacing />

		<farm-card-content
			background="lighten"
			v-for="(period, index) in pricing.periods"
			:key="`${period.id}-${period.financialVehicle}`"
		>
			<PricingRowLine :period="period" :updated-at="index === 0 ? pricing.updatedAt : ''" />
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import { productFinancialVehicle } from '../../types';

import PricingRowLine from './../PricingRowLine';

export default defineComponent({
	components: {
		PricingRowLine,
	},
	props: {
		pricing: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const { pricing } = toRefs(props);

		const productFinancialVehicle: productFinancialVehicle = {
			productFinancialVehicleId: pricing.value.periods[0].productFinancialVehicleId,
			productFinancialVehicleName: pricing.value.periods[0].productName,
		};

		return {
			productFinancialVehicle,
		};
	},
});
</script>
