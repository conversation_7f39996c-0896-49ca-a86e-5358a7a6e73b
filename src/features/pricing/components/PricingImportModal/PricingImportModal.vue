<template>
	<farm-modal v-model="isOpenedModal" persistent :offset-top="78" :offset-bottom="16">
		<template #header>
			<farm-row no-default-gutters class="pt-6">
				<farm-col cols="12" class="mb-6">
					<farm-heading :type="6">Importação de arquivo</farm-heading>
				</farm-col>
			</farm-row>
		</template>
		<template #content>
			<farm-row>
				<farm-col cols="12" align-self="center">
					<farm-bodytext tag="span" :type="2">
						Não possui a Planilha Modelo?
					</farm-bodytext>

					<farm-btn
						class="ml-4"
						title="Baixar Planilha Modelo"
						plain
						@click="downloadModelTemplate"
					>
						<farm-icon>download</farm-icon>
						Baixar Planilha Modelo
					</farm-btn>
				</farm-col>

				<farm-col cols="12" class="my-6">
					<farm-multiple-filepicker
						ref="filePicker"
						:max-file-size="10"
						:max-files-number="1"
						:accepted-file-types="EXCEL_ACCEPT_FILETYPE"
						@onFileChange="updateFiles"
						@onMaxFileSizeWarning="onMaxSizeExceededWarning"
						@onInvalidFiles="filePickerWarnings.isFileInvalid = true"
					/>
				</farm-col>

				<farm-col
					v-if="hasFilePickerWarning || hasErrorMessage"
					cols="12"
					class="d-flex flex-column pricing-import-alerts mb-6"
				>
					<farm-alertbox
						icon="alert-circle"
						color="error"
						v-if="filePickerWarnings.isMaxSizeExceeded"
					>
						Você excedeu o limite permitido de 10mb. Envie arquivos de até 10mb para
						prosseguir com a solicitação.
					</farm-alertbox>

					<farm-alertbox
						icon="alert-circle"
						color="error"
						v-if="filePickerWarnings.isFileInvalid"
					>
						O arquivo não pode ser enviado. Envie arquivos no formato: .xls, .xlsx
					</farm-alertbox>

					<template v-if="hasErrorMessage">
						<farm-alertbox icon="alert-circle" color="error">
							Houve erro na validação de algumas linhas.
						</farm-alertbox>

						<farm-textarea disabled rows="6" :value="errorMessage" />

						<farm-copytoclipboard
							class="d-inline-flex ml-auto"
							:toCopy="errorMessage"
							:isIcon="false"
						/>
					</template>
				</farm-col>

				<farm-col cols="12" align="end">
					<farm-btn title="Voltar" plain @click="$emit('onClose')">Voltar</farm-btn>
					<farm-btn
						title="Importar o arquivo"
						:disabled="isImportButtonDisabled"
						@click="uploadPricingSpreadsheet"
					>
						<farm-icon>send</farm-icon>
						Importar
					</farm-btn>
				</farm-col>
			</farm-row>

			<farm-loader mode="overlay" v-if="isLoading" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, toRefs, watch } from 'vue';
import { EXCEL_ACCEPT_FILETYPE } from '@/constants';
import { useGetter, useIsLoading, useSelectedProductId, useStore } from '@/composibles';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	props: {
		isOpenedModal: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const { isOpenedModal } = toRefs(props);

		const store = useStore();
		const selectedProduct = useSelectedProductId();

		const filePicker = ref(null);
		const files = ref([] as File[]);
		const filePickerWarnings = ref({
			isFileInvalid: false,
			isMaxSizeExceeded: false,
		});

		const uploadPricingSpreadsheetRequestStatus = computed(
			useGetter('pricing', 'uploadPricingSpreadsheetRequestStatus')
		);
		const hasFile = computed(() => files.value.length > 0);
		const hasFilePickerWarning = computed(() =>
			Object.values(filePickerWarnings.value).some(Boolean)
		);
		const isImportButtonDisabled = computed(
			() =>
				!hasFile.value ||
				hasFilePickerWarning.value ||
				uploadPricingSpreadsheetRequestStatus.value?.type === 'ERROR'
		);
		const errorMessage = computed(() => uploadPricingSpreadsheetRequestStatus.value?.message);
		const hasErrorMessage = computed(() =>
			Boolean(
				uploadPricingSpreadsheetRequestStatus.value?.type === 'ERROR' &&
					uploadPricingSpreadsheetRequestStatus.value?.message
			)
		);

		const isLoading = useIsLoading([uploadPricingSpreadsheetRequestStatus]);

		const downloadModelTemplate = (): void => {
			notification(RequestStatusEnum.SUCCESS, {
				message: 'O download do arquivo será iniciado em instantes...',
				title: 'Download',
			});
			setTimeout(() => {
				store.dispatch('pricing/downloadModelPricing', {
					idProduct: selectedProduct.value,
				});
			}, 1000);
		};
		const uploadPricingSpreadsheet = (): void => {
			resetRequestStatus();
			resetWarningsAndErrors();

			const formData = new FormData();
			formData.append('file', files.value[0], files.value[0].name);

			store.dispatch('pricing/uploadPricingSpreadsheet', {
				payload: formData,
			});
		};
		const updateFiles = async (pickerFiles: File[]) => {
			resetWarningsAndErrors();
			if (pickerFiles.length) {
				resetRequestStatus();
			}

			files.value = pickerFiles;
		};
		const onMaxSizeExceededWarning = () => {
			filePickerWarnings.value.isMaxSizeExceeded = true;
		};
		const onInvalidFileWarning = () => {
			filePickerWarnings.value.isFileInvalid = true;
		};
		const resetFile = (): void => {
			files.value = [];
			filePicker.value?.reset();
		};
		const resetRequestStatus = (): void => {
			store.commit(
				'pricing/setUploadPricingSpreadsheetRequestStatus',
				RequestStatusEnum.IDLE
			);
		};
		const resetWarningsAndErrors = (): void => {
			Object.keys(filePickerWarnings.value).forEach(
				key => (filePickerWarnings.value[key] = false)
			);
		};
		const resetForm = (): void => {
			resetFile();
			resetRequestStatus();
			resetWarningsAndErrors();
		};

		watch(isOpenedModal, resetForm);
		watch(uploadPricingSpreadsheetRequestStatus, newValue => {
			if (newValue === RequestStatusEnum.SUCCESS) {
				resetFile();
				resetRequestStatus();
			}
		});

		return {
			EXCEL_ACCEPT_FILETYPE,
			isLoading,
			errorMessage,
			files,
			filePicker,
			filePickerWarnings,
			hasFile,
			hasFilePickerWarning,
			hasErrorMessage,
			isImportButtonDisabled,
			uploadPricingSpreadsheetRequestStatus,
			onMaxSizeExceededWarning,
			onInvalidFileWarning,
			downloadModelTemplate,
			uploadPricingSpreadsheet,
			updateFiles,
			resetFile,
		};
	},
});
</script>

<style lang="scss">
.pricing-import-alerts {
	gap: 15px;
}
</style>
