<template>
	<farm-row class="pricing-line" no-default-gutters>
		<farm-col cols="12" md="2" tag="dl" class="px-md-8 pl-md-3">
			<farm-caption variation="semiBold" class="mb-md-2" tag="dt">
				Início do Período
			</farm-caption>
			<farm-caption variation="medium" tag="dd">
				{{ useDateFormatter(period.startDate) }}
			</farm-caption>
		</farm-col>
		<farm-col cols="12" md="2" tag="dl" class="px-md-8">
			<farm-caption variation="semiBold" class="mb-md-2">Fim do Período</farm-caption>
			<farm-caption variation="medium">
				{{ useDateFormatter(period.endDate) }}
			</farm-caption>
		</farm-col>
		<farm-col cols="12" md="2" tag="dl" class="px-md-8">
			<farm-caption variation="semiBold" class="mb-md-2">Taxa</farm-caption>
			<farm-caption variation="medium"> {{ period.tax }}% </farm-caption>
		</farm-col>
		<farm-col cols="12" md="2" tag="dl" class="px-md-8">
			<farm-caption variation="semiBold" class="mb-md-2">N+</farm-caption>
			<farm-caption variation="medium">{{ period.number }}</farm-caption>
		</farm-col>
		<farm-col cols="12" md="2" tag="dl" class="ml-auto">
			<template v-if="updatedAt">
				<farm-caption variation="semiBold" class="mb-md-3 text-right">
					Última atualização
				</farm-caption>
				<farm-caption variation="medium" class="text-right">
					{{ updatedAt }}
				</farm-caption>
			</template>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { useDateFormatter } from '../../composables';

export default defineComponent({
	props: {
		period: {
			type: Object,
			required: true,
		},
		updatedAt: {
			type: String,
			default: '',
		},
	},
	setup() {
		return {
			useDateFormatter,
		};
	},
});
</script>

<style lang="scss">
.pricing-line {
	gap: 20px 0;

	@media screen and (min-width: 960px) {
		> dl {
			&:not(:last-child, :nth-last-child(2)) {
				border-right: 1px solid var(--farm-stroke-base);
			}

			&:not(:last-child) {
				max-width: 155px;
			}
		}
	}
}
</style>
