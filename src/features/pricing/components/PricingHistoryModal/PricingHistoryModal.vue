<template>
	<farm-modal v-model="isOpenedModal" persistent :offset-top="16" :offset-bottom="68">
		<template #content>
			<farm-row>
				<farm-col cols="10">
					<farm-caption variation="semiBold" class="mb-6">
						Histórico {{ productFinancialVehicle.productFinancialVehicleName }}
					</farm-caption>
				</farm-col>

				<farm-col cols="2" align="end">
					<farm-icon aria-label="Fechar" role="button" @click="$emit('onClose')">
						close
					</farm-icon>
				</farm-col>
			</farm-row>

			<template v-for="(pricing, index) in pricingHistoryList">
				<farm-caption
					:key="`caption-${pricing.createdAt}-${index}`"
					variation="medium"
					class="mb-4"
				>
					{{ defaultDateFormat(pricing.createdAt) }}
				</farm-caption>

				<farm-card class="my-4" :key="`card-${pricing.createdAt}-${index}`">
					<farm-card-content
						v-for="(period, index) in pricing.hist"
						:key="`${index}-${period.startDate}-${period.endDate}`"
						background="lighten"
						gutter="sm"
					>
						<PricingRowLine :period="period" />
					</farm-card-content>
				</farm-card>
			</template>

			<farm-loader mode="overlay" v-if="isLoading" />
		</template>
		<template #footer>
			<div class="d-flex align-center justify-space-between pa-4">
				<farm-datatable-paginator
					class="mt-0"
					:has-gutter="false"
					:page="presentableFilterValues.pageNumber"
					:total-pages="filters.totalPages"
					hide-per-page-options
					@onChangePage="onChangePage"
				/>
				<farm-btn plain @click="$emit('onClose')"> Fechar </farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { PropType, computed, defineComponent, onMounted, toRefs } from 'vue';
import { useGetter, useIsLoading, useStore } from '@/composibles';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import { RequestQueryString } from '@/types';

import PricingRowLine from '../PricingRowLine';
import { productFinancialVehicle } from '../../types';
import { usePageable } from '../../composables';

export default defineComponent({
	components: {
		PricingRowLine,
	},
	props: {
		isOpenedModal: {
			type: Boolean,
			default: false,
		},
		productFinancialVehicle: {
			type: Object as PropType<productFinancialVehicle>,
			required: true,
		},
	},
	setup(props) {
		const { productFinancialVehicle } = toRefs(props);
		const store = useStore();

		const pricingHistoryList = computed(useGetter('pricing', 'pricingHistoryList'));
		const pricingHistoryPageable = computed(useGetter('pricing', 'pricingHistoryPageable'));
		const fetchPricingHistoryRequestStatus = computed(
			useGetter('pricing', 'fetchPricingHistoryRequestStatus')
		);
		const hasHistory = computed(() => pricingHistoryList.value.length);

		const isLoading = useIsLoading([fetchPricingHistoryRequestStatus]);

		const fetchPricingHistory = (params?: RequestQueryString) =>
			store.dispatch('pricing/fetchPricingHistory', {
				params,
				payload: {
					productFinancialVehicleId:
						productFinancialVehicle.value.productFinancialVehicleId,
				},
			});

		const { filters, presentableFilterValues, onChangePage } = usePageable(
			fetchPricingHistory,
			pricingHistoryPageable
		);

		onMounted(() => {
			store.commit('pricing/setPricingHistoryList', []);
			const initialParams = new URLSearchParams({
				limit: '20',
			});

			fetchPricingHistory(initialParams);
		});

		return {
			pricingHistoryList,
			pricingHistoryPageable,
			isLoading,
			hasHistory,
			filters,
			presentableFilterValues,
			defaultDateFormat,
			onChangePage,
			fetchPricingHistory,
		};
	},
});
</script>
