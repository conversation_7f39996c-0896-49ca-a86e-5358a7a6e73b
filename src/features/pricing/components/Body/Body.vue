<template>
	<farm-container>
		<farm-row justify="space-between">
			<farm-col cols="12" md="8" lg="6">
				<farm-form-mainfilter
					:has-extra-filters="false"
					label="Buscar Produto"
					@onInputChange="handleSearch"
				/>
			</farm-col>

			<farm-col cols="12" md="4" class="d-md-flex justify-end mb-7 mt-md-7 mb-md-0">
				<farm-btn-export @onClick="handleExport" class="mb-2 mb-sm-0 mr-sm-2" />
				<farm-btn-import @onClick="toggleModalStatus('download')" />
			</farm-col>
		</farm-row>

		<template v-if="hasPricings">
			<PricingCard
				v-for="pricing in pricings"
				:key="`${pricing.financialVehicleId}-${pricing.productFinancialVehicleId}-${pricing.periods.length}`"
				:pricing="pricing"
				@toggle-history-modal="handleHistoryModal"
			/>
		</template>
		<farm-emptywrapper v-else />

		<farm-datatable-paginator
			v-if="hasPricings"
			:has-gutter="false"
			:page="presentableFilterValues.pageNumber"
			:total-pages="filters.totalPages"
			@onChangePage="onChangePage"
			@onChangeLimitPerPage="onChangePageLimit"
		/>

		<PricingHistoryModal
			v-if="isOpenedModal.history"
			:product-financial-vehicle="historyModalProductFinancialVehicle"
			:is-opened-modal="isOpenedModal.history"
			@onClose="closeModal('history')"
		/>

		<PricingImportModal
			:is-opened-modal="isOpenedModal.download"
			@onClose="closeModal('download')"
		/>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from 'vue';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { useGetter } from '@/composibles';

import PricingCard from '../PricingCard';
import PricingRowLine from '../PricingRowLine';
import PricingHistoryModal from '../PricingHistoryModal';
import PricingImportModal from '../PricingImportModal';
import { usePaginationConstructor, usePricing, usePricingExport } from './../../composables';
import { productFinancialVehicle } from '../../types';

export default defineComponent({
	components: {
		PricingRowLine,
		PricingHistoryModal,
		PricingImportModal,
		PricingCard,
	},
	setup() {
		const {
			pricings,
			hasPricings,
			isLoading,
			handleSearchPricing,
			fetchPricings,
			resetPricingList,
		} = usePricing();

		const {
			filters,
			presentableFilterValues,
			content,
			isLoading: isFakeLoading,
			resetPagination,
			onChangePage,
			onChangePageLimit,
		} = usePaginationConstructor(pricings);

		const historyModalProductFinancialVehicle = ref(null as null | productFinancialVehicle);

		const { handleExport } = usePricingExport();

		const isOpenedModal = ref({
			history: false,
			download: false,
		});

		const uploadPricingSpreadsheetRequestStatus = computed(
			useGetter('pricing', 'uploadPricingSpreadsheetRequestStatus')
		);

		const handleHistoryModal = (productFinancialVehicle: productFinancialVehicle) => {
			historyModalProductFinancialVehicle.value = productFinancialVehicle;

			toggleModalStatus('history');
		};
		const closeModal = (modal: string) => {
			isOpenedModal.value[modal] = false;
		};
		const toggleModalStatus = (modal: string) => {
			isOpenedModal.value[modal] = !isOpenedModal.value[modal];
		};
		const handleSearch = (value: string) => {
			resetPagination();

			handleSearchPricing(value);
		};

		watch(uploadPricingSpreadsheetRequestStatus, newValue => {
			const isPartialError =
				newValue?.type === RequestStatusEnum.ERROR && newValue?.httpStatus === 200;
			if (isPartialError) {
				resetPricingList(resetPagination);
			}
			if (newValue === RequestStatusEnum.SUCCESS) {
				closeModal('download');
				resetPricingList(resetPagination);
			}
		});

		onMounted(() => {
			fetchPricings();
		});

		return {
			pricings: content || pricings,
			hasPricings,
			isOpenedModal,
			isLoading: isFakeLoading || isLoading,
			historyModalProductFinancialVehicle,
			filters,
			presentableFilterValues,
			closeModal,
			toggleModalStatus,
			onChangePage,
			onChangePageLimit,
			handleSearch,
			handleHistoryModal,
			handleExport,
		};
	},
});
</script>

<style scoped lang="scss">
@import './Body.scss';
</style>
