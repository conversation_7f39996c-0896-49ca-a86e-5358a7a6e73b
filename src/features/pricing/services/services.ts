import excelService from '@/configurations/services/excel';
import configurationService from '@/configurations/services/configurations';
import { RequestQueryString } from '@/types';
import { fetchPricingHistoryParameters } from '../types';
import { file as downloadFileHandler, environment } from '@farm-investimentos/front-mfe-libs-ts';

export const downloadModelPricing = async () => {
	const baseURL = environment.apiExcelUrl;
	return downloadFileHandler(`${baseURL}/v1/products/excel/model/pricing`);
};

export const exportPricingSpreadsheet = async (params?: RequestQueryString) => {
	const baseURL = environment.apiExcelUrl;
	const queryParams = new URLSearchParams(params);
	return downloadFileHandler(`${baseURL}/v1/products/excel/model/pricing/product?${queryParams}`);
};

export const uploadPricingSpreadsheet = async (payload: FormData, params?: RequestQueryString) => {
	return excelService.post(`/v1/products/contracts/excel/pricing/upload`, payload, {
		params,
	});
};

export const fetchPricings = async (params?: RequestQueryString) => {
	return configurationService.get('/v1/products/settings/pricing/group/', {
		params,
	});
};

export const fetchPricingHistory = async (
	payload: fetchPricingHistoryParameters['payload'],
	params?: fetchPricingHistoryParameters['params']
) => {
	return configurationService.get(
		`/v1/products/${payload.productFinancialVehicleId}/settings/pricing/history`,
		{
			params,
		}
	);
};
