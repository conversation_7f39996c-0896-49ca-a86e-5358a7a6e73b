<template>
	<farm-card class="pa-4">
		<div class="d-flex justify-space-between mb-4">
			<farm-bodytext type="2" variation="bold">
				{{ product.vehicleName }}
			</farm-bodytext>
			<farm-btn
				icon
				plain
				class="open-history-modal-trigger mr-n2"
				aria-label="Abrir o histórico de alterações"
				@click="$emit('open-modal', product.idVehicle)"
			>
				<farm-icon size="16">open-in-new</farm-icon>
			</farm-btn>
		</div>

		<farm-typography
			weight="600"
			tag="label"
			size="sm"
			color="black"
			color-variation="50"
			:for="`datepicker-id-${product.idVehicle}`"
		>
			Data(s) de vencimento
		</farm-typography>
		<farm-input-datepicker
			class="update-dates-trigger mt-2"
			multiple
			required
			:position="datepickerPosition"
			:input-id="`datepicker-id-${product.idVehicle}`"
			v-model="product.dates"
			@input="updateDateList"
		/>
	</farm-card>
</template>

<script lang="ts">
import { useStore } from '@/composibles';
import { defineComponent } from 'vue';

import type { PropType } from 'vue';
import type { UpdateDatesRequest } from '../../services/types';
import type { DatesByProduct } from '../../types';

export default defineComponent({
	props: {
		product: {
			type: Object as PropType<DatesByProduct>,
			required: true,
		},
		datepickerPosition: {
			type: String,
			default: 'bottom',
		},
	},
	setup(props) {
		const store = useStore();

		const updateDateList = (dates: string[]) => {
			const request: UpdateDatesRequest = {
				params: {
					productId: props.product.idVehicle,
				},
				payload: dates,
			};

			store.dispatch('dates/updateDatesList', request);
		};

		return {
			updateDateList,
		};
	},
});
</script>

<style lang="scss" scoped>
@import 'DateCard';
</style>
