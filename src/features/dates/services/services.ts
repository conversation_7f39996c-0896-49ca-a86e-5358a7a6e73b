import client from '@/configurations/services/configurations';
import type { DatesHistoryRequest, UpdateDatesRequest } from './types';
import type { DateHistory, DatesByProduct } from '../types';

export const fetchDates = () => {
	return client.get<{ data: { content: DatesByProduct[] } }>(
		'/v1/origination/datepicker/list/all'
	);
};

export const fetchDatesHistory = ({ params }: DatesHistoryRequest) => {
	return client.get<{ data: { content: DateHistory[] } }>(
		`/v1/origination/${params.productId}/datepicker/history`
	);
};

export const updateDatesList = ({ payload, params }: UpdateDatesRequest) => {
	return client.post(`/v1/origination/${params.productId}/datepicker/new`, payload);
};
