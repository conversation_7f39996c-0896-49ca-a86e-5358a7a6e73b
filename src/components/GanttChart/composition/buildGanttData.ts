import { computed } from 'vue';
import { isValid, endOfMonth, addMonths } from 'date-fns';

export default function buildGanttData(props) {
	const autoCalculatedDateRange = computed(() => {
		const allDates: Date[] = [];

		props.data.groups.forEach(group => {
			group.bars.forEach(bar => {
				const startDate = bar.start instanceof Date ? bar.start : new Date(bar.start);
				const endDate = bar.end instanceof Date ? bar.end : new Date(bar.end);

				if (isValid(startDate)) {
					allDates.push(startDate);
				}
				if (isValid(endDate)) {
					allDates.push(endDate);
				}
			});
		});

		if (allDates.length === 0) {
			const now = new Date();
			return {
				start: new Date(now.getFullYear(), 0, 1),
				end: new Date(now.getFullYear(), 11, 31),
			};
		}

		const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
		let maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));

		minDate.setDate(1);
		maxDate = endOfMonth(maxDate);

		// Add one extra month at the end for better visualization
		maxDate = endOfMonth(addMonths(maxDate, 1));

		return { start: minDate, end: maxDate };
	});

	const autoGeneratedLegend = computed(() => {
		const uniqueItems = new Map<string, { label: string; color: string }>();

		props.data.groups.forEach(group => {
			group.bars.forEach(bar => {
				const key = `${bar.color}-${bar.label}`;
				if (!uniqueItems.has(key)) {
					uniqueItems.set(key, {
						label: bar.label,
						color: bar.color,
					});
				}
			});
		});

		return Array.from(uniqueItems.values());
	});

	return {
		autoCalculatedDateRange,
		autoGeneratedLegend,
	};
}
